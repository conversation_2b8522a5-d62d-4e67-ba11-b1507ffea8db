
# Resume Preview Generator 云托管版本部署指南

## 1. 部署云托管服务

### 构建和推送镜像
```bash
cd cloud-run/resume-snapshot
chmod +x deploy.sh
./deploy.sh
```

### 云托管控制台配置
1. 登录腾讯云云托管控制台
2. 创建服务 `resume-snapshot`
3. 配置参数:
   - 镜像: ccr.ccs.tencentyun.com/your-namespace/resume-snapshot:latest
   - 端口: 80
   - 最小实例: 0
   - 最大实例: 5
   - CPU: 1核
   - 内存: 2GB

## 2. 部署云函数

### 使用云开发控制台
1. 进入云开发控制台
2. 选择云函数
3. 更新 `resumePreviewGenerator` 函数
4. 上传新的代码

### 使用命令行工具
```bash
tcb fn deploy resumePreviewGenerator --dir cloudfunctions/resumePreviewGenerator
```

## 3. 测试验证

### 云函数测试
在云开发控制台的云函数页面，使用以下测试数据:

```json
{
  "resumeData": {
  "personalInfo": {
    "name": "张三",
    "email": "<EMAIL>",
    "phone": "13800138000",
    "address": "北京市朝阳区"
  },
  "summary": "资深前端开发工程师，具有5年以上React、Vue.js开发经验，熟悉微信小程序开发，具备良好的团队协作能力和项目管理经验。",
  "workExperience": [
    {
      "position": "高级前端工程师",
      "company": "某科技有限公司",
      "startDate": "2020-01",
      "endDate": "至今",
      "description": "负责公司核心产品的前端开发工作，参与架构设计，优化用户体验，提升页面性能。"
    },
    {
      "position": "前端工程师",
      "company": "某互联网公司",
      "startDate": "2018-06",
      "endDate": "2019-12",
      "description": "参与多个Web应用的开发，使用React技术栈，负责组件开发和页面优化。"
    }
  ],
  "education": [
    {
      "degree": "本科",
      "major": "计算机科学与技术",
      "school": "某大学",
      "startDate": "2014-09",
      "endDate": "2018-06"
    }
  ],
  "skills": [
    "JavaScript",
    "TypeScript",
    "React",
    "Vue.js",
    "Node.js",
    "HTML5",
    "CSS3",
    "Webpack",
    "微信小程序",
    "Git",
    "MySQL",
    "MongoDB"
  ]
},
  "format": "png"
}
```

### 云托管服务测试
```bash
cd cloud-run/resume-snapshot
node test-service.js
```

## 4. 监控和日志

### 云函数日志
- 云开发控制台 > 云函数 > resumePreviewGenerator > 日志
- 查看调用成功率和错误信息

### 云托管日志
- 云托管控制台 > resume-snapshot > 日志
- 监控容器运行状态和性能指标

## 5. 性能优化建议

1. **云托管优化**:
   - 设置合理的实例数量
   - 监控内存使用情况
   - 优化镜像大小

2. **云函数优化**:
   - 减少冷启动时间
   - 优化HTML生成逻辑
   - 合理设置超时时间

3. **整体架构**:
   - 使用CDN加速静态资源
   - 实现缓存机制
   - 监控服务可用性

## 6. 故障排查

### 常见问题
1. 云托管服务无法访问
   - 检查服务状态
   - 验证网络配置
   - 查看容器日志

2. 截图生成失败
   - 检查目标URL可访问性
   - 验证Puppeteer配置
   - 查看内存使用情况

3. 云函数调用失败
   - 检查权限配置
   - 验证环境变量
   - 查看函数日志
