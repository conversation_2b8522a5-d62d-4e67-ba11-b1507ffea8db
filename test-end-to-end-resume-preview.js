/**
 * 端到端简历PNG图片生成功能测试
 * 验证从云函数到云托管容器的完整调用链路
 */

const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');

// 测试配置
const TEST_CONFIG = {
    cloudRun: {
        localPort: 80,
        serviceName: 'resume-snapshot',
        testTimeout: 30000
    },
    cloudFunction: {
        name: 'resumePreviewGenerator',
        timeout: 60000
    },
    output: {
        directory: './test-output',
        reportFile: 'end-to-end-test-report.json'
    }
};

// 真实测试数据
const REAL_RESUME_DATA = {
    personalInfo: {
        name: '李明',
        email: '<EMAIL>',
        phone: '13800138000',
        address: '北京市海淀区中关村软件园',
        github: 'https://github.com/liming',
        linkedin: 'https://linkedin.com/in/liming'
    },
    summary: '资深全栈开发工程师，拥有8年互联网产品开发经验。精通React、Vue.js、Node.js等前端技术栈，熟悉微服务架构和云原生技术。具备优秀的团队协作能力和项目管理经验，曾主导多个大型项目的架构设计和技术选型。',
    workExperience: [
        {
            position: '高级全栈工程师',
            company: '腾讯科技有限公司',
            startDate: '2021-03',
            endDate: '至今',
            description: '负责微信生态相关产品的前后端开发工作，参与小程序云开发平台的架构设计。主要技术栈包括React、Node.js、云开发等。成功优化系统性能，用户响应时间提升40%。'
        },
        {
            position: '前端技术专家',
            company: '阿里巴巴集团',
            startDate: '2019-06',
            endDate: '2021-02',
            description: '负责淘宝前端基础设施建设，开发了多个内部工具和组件库。参与双11大促技术保障，确保系统稳定性。推动前端工程化建设，提升团队开发效率30%。'
        },
        {
            position: '前端工程师',
            company: '字节跳动',
            startDate: '2017-08',
            endDate: '2019-05',
            description: '参与今日头条和抖音等产品的前端开发工作。负责用户增长相关功能的开发和优化，通过A/B测试持续改进用户体验。掌握React、Vue.js等主流前端框架。'
        }
    ],
    education: [
        {
            degree: '硕士',
            major: '计算机科学与技术',
            school: '清华大学',
            startDate: '2015-09',
            endDate: '2017-06',
            gpa: '3.8/4.0',
            achievements: ['优秀毕业生', '国家奖学金', 'ACM程序设计竞赛金奖']
        },
        {
            degree: '学士',
            major: '软件工程',
            school: '北京理工大学',
            startDate: '2011-09',
            endDate: '2015-06',
            gpa: '3.9/4.0',
            achievements: ['专业第一名', '校级优秀学生干部']
        }
    ],
    skills: [
        'JavaScript', 'TypeScript', 'React', 'Vue.js', 'Angular',
        'Node.js', 'Express', 'Koa', 'Next.js', 'Nuxt.js',
        'HTML5', 'CSS3', 'SCSS', 'Less', 'Tailwind CSS',
        'Webpack', 'Vite', 'Rollup', 'Babel', 'ESLint',
        'Git', 'Docker', 'Kubernetes', 'AWS', '腾讯云',
        'MySQL', 'MongoDB', 'Redis', 'PostgreSQL',
        '微信小程序', '云开发', 'Serverless', 'GraphQL'
    ],
    projects: [
        {
            name: '企业级前端脚手架',
            description: '基于React和TypeScript的企业级前端开发脚手架，集成了完整的开发工具链和最佳实践。',
            technologies: ['React', 'TypeScript', 'Webpack', 'Jest'],
            achievements: ['团队采用率100%', '开发效率提升50%']
        },
        {
            name: '微服务监控平台',
            description: '基于Node.js和Vue.js的微服务监控平台，提供实时监控、告警和日志分析功能。',
            technologies: ['Vue.js', 'Node.js', 'MongoDB', 'Docker'],
            achievements: ['监控服务200+', '故障发现时间缩短80%']
        }
    ],
    certifications: [
        '腾讯云开发者认证',
        'AWS Solutions Architect',
        'PMP项目管理专业人士'
    ],
    languages: [
        { language: '中文', level: '母语' },
        { language: '英语', level: 'CET-6，流利' },
        { language: '日语', level: 'N2' }
    ]
};

// 测试结果收集器
class TestResultCollector {
    constructor() {
        this.results = {
            startTime: new Date().toISOString(),
            tests: [],
            summary: {
                total: 0,
                passed: 0,
                failed: 0,
                duration: 0
            }
        };
    }

    addTest(name, status, details = {}) {
        const test = {
            name,
            status,
            timestamp: new Date().toISOString(),
            duration: details.duration || 0,
            details
        };

        this.results.tests.push(test);
        this.results.summary.total++;

        if (status === 'PASSED') {
            this.results.summary.passed++;
        } else {
            this.results.summary.failed++;
        }

        console.log(`${status === 'PASSED' ? '✅' : '❌'} ${name}`);
        if (details.message) {
            console.log(`   ${details.message}`);
        }
    }

    generateReport() {
        this.results.endTime = new Date().toISOString();
        this.results.summary.duration = new Date(this.results.endTime) - new Date(this.results.startTime);

        // 确保输出目录存在
        if (!fs.existsSync(TEST_CONFIG.output.directory)) {
            fs.mkdirSync(TEST_CONFIG.output.directory, { recursive: true });
        }

        // 保存报告
        const reportPath = path.join(TEST_CONFIG.output.directory, TEST_CONFIG.output.reportFile);
        fs.writeFileSync(reportPath, JSON.stringify(this.results, null, 2));

        console.log('\n📊 测试总结:');
        console.log(`   总计: ${this.results.summary.total}`);
        console.log(`   通过: ${this.results.summary.passed}`);
        console.log(`   失败: ${this.results.summary.failed}`);
        console.log(`   耗时: ${Math.round(this.results.summary.duration / 1000)}秒`);
        console.log(`   报告: ${reportPath}`);

        return this.results;
    }
}

/**
 * 检查Docker是否可用
 */
async function checkDockerAvailable() {
    return new Promise((resolve) => {
        const docker = spawn('docker', ['--version']);
        docker.on('close', (code) => {
            resolve(code === 0);
        });
        docker.on('error', () => {
            resolve(false);
        });
    });
}

/**
 * 启动本地云托管容器
 */
async function startLocalContainer() {
    return new Promise((resolve, reject) => {
        console.log('🐳 启动本地云托管容器...');

        const containerPath = path.join(__dirname, 'cloud-run/resume-snapshot');

        // 检查容器目录是否存在
        if (!fs.existsSync(containerPath)) {
            reject(new Error('云托管容器目录不存在'));
            return;
        }

        // 构建容器
        const build = spawn('docker', ['build', '-t', 'resume-snapshot-test', '.'], {
            cwd: containerPath,
            stdio: 'pipe'
        });

        build.on('close', (code) => {
            if (code !== 0) {
                reject(new Error('容器构建失败'));
                return;
            }

            // 运行容器
            const run = spawn('docker', [
                'run', '-d', '-p', `${TEST_CONFIG.cloudRun.localPort}:80`,
                '--name', 'resume-snapshot-test-container',
                'resume-snapshot-test'
            ]);

            run.on('close', (runCode) => {
                if (runCode === 0) {
                    console.log('✅ 容器启动成功');
                    // 等待容器完全启动
                    setTimeout(() => resolve(), 3000);
                } else {
                    reject(new Error('容器运行失败'));
                }
            });
        });

        build.on('error', (error) => {
            reject(error);
        });
    });
}

/**
 * 停止本地容器
 */
async function stopLocalContainer() {
    return new Promise((resolve) => {
        console.log('🛑 停止本地容器...');

        const stop = spawn('docker', ['stop', 'resume-snapshot-test-container']);
        stop.on('close', () => {
            const remove = spawn('docker', ['rm', 'resume-snapshot-test-container']);
            remove.on('close', () => {
                console.log('✅ 容器已停止并清理');
                resolve();
            });
        });
    });
}

/**
 * 测试云托管服务健康检查
 */
async function testCloudRunHealth() {
    try {
        const fetch = (await import('node-fetch')).default;
        const response = await fetch(`http://localhost:${TEST_CONFIG.cloudRun.localPort}/health`, {
            timeout: 5000
        });

        if (response.ok) {
            const data = await response.json();
            return { success: true, data };
        } else {
            return { success: false, error: `HTTP ${response.status}` };
        }
    } catch (error) {
        return { success: false, error: error.message };
    }
}

/**
 * 测试云托管截图服务
 */
async function testCloudRunSnapshot(htmlContent) {
    try {
        const fetch = (await import('node-fetch')).default;

        // 创建测试HTML文件
        const testHtmlPath = path.join(TEST_CONFIG.output.directory, 'test-resume.html');
        fs.writeFileSync(testHtmlPath, htmlContent);

        const testData = {
            url: `file://${testHtmlPath}`,
            format: 'png',
            width: 1240,
            height: 1754,
            options: {
                fullPage: true,
                quality: 90,
                delay: 2000,
                deviceScaleFactor: 2
            }
        };

        const startTime = Date.now();
        const response = await fetch(`http://localhost:${TEST_CONFIG.cloudRun.localPort}/snapshot`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(testData),
            timeout: TEST_CONFIG.cloudRun.testTimeout
        });

        const duration = Date.now() - startTime;

        if (response.ok) {
            const buffer = await response.buffer();
            const outputPath = path.join(TEST_CONFIG.output.directory, `test-snapshot-${Date.now()}.png`);
            fs.writeFileSync(outputPath, buffer);

            return {
                success: true,
                data: {
                    fileSize: buffer.length,
                    outputPath,
                    duration,
                    dimensions: testData
                }
            };
        } else {
            const errorText = await response.text();
            return { success: false, error: `HTTP ${response.status}: ${errorText}` };
        }
    } catch (error) {
        return { success: false, error: error.message };
    }
}

/**
 * 生成简历HTML内容
 */
function generateResumeHTML(resumeData) {
    const { personalInfo, summary, workExperience, education, skills, projects } = resumeData;

    return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${personalInfo.name} - 简历</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Microsoft YaHei', 'PingFang SC', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #fff;
            padding: 40px;
            max-width: 800px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            border-bottom: 3px solid #2c3e50;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .name {
            font-size: 36px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #2c3e50;
        }
        .contact {
            color: #7f8c8d;
            font-size: 16px;
            display: flex;
            justify-content: center;
            gap: 20px;
            flex-wrap: wrap;
        }
        .section {
            margin: 30px 0;
        }
        .section-title {
            font-size: 22px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #2c3e50;
            border-left: 4px solid #3498db;
            padding-left: 12px;
        }
        .section-content {
            margin-left: 16px;
        }
        .work-item, .edu-item, .project-item {
            margin-bottom: 25px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #3498db;
        }
        .item-title {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 8px;
            font-size: 18px;
        }
        .item-company, .item-school {
            color: #3498db;
            margin-bottom: 5px;
            font-weight: 600;
        }
        .item-date {
            color: #7f8c8d;
            font-size: 14px;
            margin-bottom: 12px;
        }
        .item-description {
            line-height: 1.7;
        }
        .skills-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 10px;
            margin-top: 10px;
        }
        .skill-tag {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            padding: 8px 12px;
            border-radius: 20px;
            font-size: 14px;
            text-align: center;
            font-weight: 500;
        }
        .summary-text {
            background: #ecf0f1;
            padding: 20px;
            border-radius: 8px;
            font-style: italic;
            line-height: 1.8;
        }
        .achievements {
            margin-top: 10px;
        }
        .achievement-item {
            background: #e8f5e8;
            color: #27ae60;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            display: inline-block;
            margin: 2px 4px;
        }
        @media print {
            body { padding: 20px; }
            .work-item, .edu-item, .project-item { break-inside: avoid; }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="name">${personalInfo.name}</div>
        <div class="contact">
            <span>📧 ${personalInfo.email}</span>
            <span>📱 ${personalInfo.phone}</span>
            <span>📍 ${personalInfo.address}</span>
            ${personalInfo.github ? `<span>🔗 ${personalInfo.github}</span>` : ''}
        </div>
    </div>

    <div class="section">
        <div class="section-title">个人简介</div>
        <div class="section-content">
            <div class="summary-text">${summary}</div>
        </div>
    </div>

    <div class="section">
        <div class="section-title">工作经历</div>
        <div class="section-content">
            ${workExperience.map(work => `
                <div class="work-item">
                    <div class="item-title">${work.position}</div>
                    <div class="item-company">${work.company}</div>
                    <div class="item-date">${work.startDate} - ${work.endDate}</div>
                    <div class="item-description">${work.description}</div>
                </div>
            `).join('')}
        </div>
    </div>

    <div class="section">
        <div class="section-title">教育背景</div>
        <div class="section-content">
            ${education.map(edu => `
                <div class="edu-item">
                    <div class="item-title">${edu.degree} - ${edu.major}</div>
                    <div class="item-school">${edu.school}</div>
                    <div class="item-date">${edu.startDate} - ${edu.endDate}</div>
                    ${edu.gpa ? `<div>GPA: ${edu.gpa}</div>` : ''}
                    ${edu.achievements ? `
                        <div class="achievements">
                            ${edu.achievements.map(achievement =>
        `<span class="achievement-item">${achievement}</span>`
    ).join('')}
                        </div>
                    ` : ''}
                </div>
            `).join('')}
        </div>
    </div>

    <div class="section">
        <div class="section-title">专业技能</div>
        <div class="section-content">
            <div class="skills-grid">
                ${skills.map(skill => `<div class="skill-tag">${skill}</div>`).join('')}
            </div>
        </div>
    </div>

    ${projects && projects.length > 0 ? `
    <div class="section">
        <div class="section-title">项目经验</div>
        <div class="section-content">
            ${projects.map(project => `
                <div class="project-item">
                    <div class="item-title">${project.name}</div>
                    <div class="item-description">${project.description}</div>
                    <div style="margin-top: 10px;">
                        <strong>技术栈:</strong> ${project.technologies.join(', ')}
                    </div>
                    ${project.achievements ? `
                        <div class="achievements">
                            ${project.achievements.map(achievement =>
        `<span class="achievement-item">${achievement}</span>`
    ).join('')}
                        </div>
                    ` : ''}
                </div>
            `).join('')}
        </div>
    </div>
    ` : ''}
</body>
</html>`;
}

/**
 * 模拟云函数调用
 */
async function simulateCloudFunctionCall(resumeData) {
    try {
        console.log('🔧 模拟云函数调用...');

        // 生成HTML内容
        const htmlContent = generateResumeHTML(resumeData);

        // 模拟上传HTML到云存储并获取临时URL
        const htmlPath = path.join(TEST_CONFIG.output.directory, `resume-${Date.now()}.html`);
        fs.writeFileSync(htmlPath, htmlContent);
        const mockTempUrl = `file://${htmlPath}`;

        console.log('📄 HTML已生成，模拟临时URL:', mockTempUrl);

        // 调用云托管截图服务
        const snapshotResult = await testCloudRunSnapshot(htmlContent);

        if (snapshotResult.success) {
            // 模拟上传PNG到云存储
            const mockFileID = `cloud://zemuresume-4gjvx1wea78e3d1e.7a65-zemuresume-4gjvx1wea78e3d1e-1234567890/resume-previews/resume-preview-${Date.now()}.png`;
            const mockImageUrl = `https://7a65-zemuresume-4gjvx1wea78e3d1e-1234567890.tcb.qcloud.la/resume-previews/resume-preview-${Date.now()}.png`;

            return {
                success: true,
                data: {
                    png: {
                        imageUrl: mockImageUrl,
                        fileID: mockFileID,
                        cloudPath: `resume-previews/resume-preview-${Date.now()}.png`,
                        fileSize: snapshotResult.data.fileSize
                    },
                    sourceUrl: mockTempUrl,
                    templateType: 'professional',
                    generatedAt: new Date().toISOString(),
                    mode: 'cloud-run-container',
                    service: 'resume-snapshot',
                    localTestPath: snapshotResult.data.outputPath
                }
            };
        } else {
            return { success: false, error: snapshotResult.error };
        }
    } catch (error) {
        return { success: false, error: error.message };
    }
}

/**
 * 测试异常情况处理
 */
async function testErrorHandling() {
    const tests = [];

    try {
        const fetch = (await import('node-fetch')).default;

        // 测试无效URL
        console.log('🧪 测试无效URL处理...');
        const invalidUrlTest = await fetch(`http://localhost:${TEST_CONFIG.cloudRun.localPort}/snapshot`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ url: '', format: 'png' }),
            timeout: 5000
        });

        tests.push({
            name: '无效URL处理',
            success: !invalidUrlTest.ok && invalidUrlTest.status === 400,
            status: invalidUrlTest.status
        });

        // 测试无效格式
        console.log('🧪 测试无效格式处理...');
        const invalidFormatTest = await fetch(`http://localhost:${TEST_CONFIG.cloudRun.localPort}/snapshot`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ url: 'https://www.baidu.com', format: 'invalid' }),
            timeout: 5000
        });

        tests.push({
            name: '无效格式处理',
            success: invalidFormatTest.ok, // 应该默认使用png格式
            status: invalidFormatTest.status
        });

        // 测试不存在的端点
        console.log('🧪 测试404处理...');
        const notFoundTest = await fetch(`http://localhost:${TEST_CONFIG.cloudRun.localPort}/nonexistent`, {
            timeout: 5000
        });

        tests.push({
            name: '404错误处理',
            success: notFoundTest.status === 404,
            status: notFoundTest.status
        });

    } catch (error) {
        tests.push({
            name: '异常处理测试',
            success: false,
            error: error.message
        });
    }

    return tests;
}

/**
 * 性能测试
 */
async function performanceTest() {
    const results = {
        tests: [],
        summary: {
            avgResponseTime: 0,
            minResponseTime: Infinity,
            maxResponseTime: 0,
            successRate: 0
        }
    };

    console.log('⚡ 开始性能测试...');

    const testCount = 3;
    const responseTimes = [];
    let successCount = 0;

    for (let i = 0; i < testCount; i++) {
        console.log(`   测试 ${i + 1}/${testCount}...`);

        const startTime = Date.now();
        const result = await simulateCloudFunctionCall(REAL_RESUME_DATA);
        const duration = Date.now() - startTime;

        responseTimes.push(duration);

        if (result.success) {
            successCount++;
        }

        results.tests.push({
            iteration: i + 1,
            duration,
            success: result.success,
            fileSize: result.success ? result.data.png.fileSize : null
        });

        // 避免过于频繁的请求
        if (i < testCount - 1) {
            await new Promise(resolve => setTimeout(resolve, 1000));
        }
    }

    // 计算统计数据
    results.summary.avgResponseTime = Math.round(responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length);
    results.summary.minResponseTime = Math.min(...responseTimes);
    results.summary.maxResponseTime = Math.max(...responseTimes);
    results.summary.successRate = Math.round((successCount / testCount) * 100);

    return results;
}

/**
 * 主测试函数
 */
async function runEndToEndTest() {
    const collector = new TestResultCollector();

    console.log('🚀 开始端到端简历PNG图片生成功能测试');
    console.log('='.repeat(80));

    // 确保输出目录存在
    if (!fs.existsSync(TEST_CONFIG.output.directory)) {
        fs.mkdirSync(TEST_CONFIG.output.directory, { recursive: true });
    }

    try {
        // 1. 检查Docker环境
        console.log('\n📋 步骤1: 检查Docker环境');
        const dockerAvailable = await checkDockerAvailable();
        collector.addTest('Docker环境检查', dockerAvailable ? 'PASSED' : 'FAILED', {
            message: dockerAvailable ? 'Docker可用' : 'Docker不可用，将跳过容器测试'
        });

        if (!dockerAvailable) {
            console.log('⚠️  Docker不可用，将进行模拟测试');

            // 生成HTML并保存
            const htmlContent = generateResumeHTML(REAL_RESUME_DATA);
            const htmlPath = path.join(TEST_CONFIG.output.directory, 'test-resume-no-docker.html');
            fs.writeFileSync(htmlPath, htmlContent);

            collector.addTest('HTML生成测试', 'PASSED', {
                message: `HTML已生成: ${htmlPath}`,
                fileSize: Buffer.from(htmlContent).length
            });

            console.log('\n📋 测试总结（模拟模式）:');
            console.log('✅ HTML生成功能正常');
            console.log('⚠️  需要部署云托管服务后进行完整测试');
            console.log(`📄 生成的HTML文件: ${htmlPath}`);

            return collector.generateReport();
        }

        // 2. 启动本地容器
        console.log('\n📋 步骤2: 启动本地云托管容器');
        try {
            await startLocalContainer();
            collector.addTest('容器启动', 'PASSED', {
                message: `容器在端口${TEST_CONFIG.cloudRun.localPort}启动成功`
            });
        } catch (error) {
            collector.addTest('容器启动', 'FAILED', {
                message: error.message
            });
            return collector.generateReport();
        }

        // 3. 健康检查
        console.log('\n📋 步骤3: 云托管服务健康检查');
        const healthResult = await testCloudRunHealth();
        collector.addTest('健康检查', healthResult.success ? 'PASSED' : 'FAILED', {
            message: healthResult.success ? '服务健康' : healthResult.error,
            data: healthResult.data
        });

        if (!healthResult.success) {
            await stopLocalContainer();
            return collector.generateReport();
        }

        // 4. 端到端功能测试
        console.log('\n📋 步骤4: 端到端功能测试');
        const e2eResult = await simulateCloudFunctionCall(REAL_RESUME_DATA);
        collector.addTest('端到端功能', e2eResult.success ? 'PASSED' : 'FAILED', {
            message: e2eResult.success ?
                `PNG生成成功，文件大小: ${Math.round(e2eResult.data.png.fileSize / 1024)}KB` :
                e2eResult.error,
            data: e2eResult.data,
            duration: e2eResult.duration
        });

        // 5. 异常处理测试
        console.log('\n📋 步骤5: 异常处理测试');
        const errorTests = await testErrorHandling();
        errorTests.forEach(test => {
            collector.addTest(`异常处理-${test.name}`, test.success ? 'PASSED' : 'FAILED', {
                message: `HTTP状态码: ${test.status}`,
                error: test.error
            });
        });

        // 6. 性能测试
        console.log('\n📋 步骤6: 性能测试');
        const perfResult = await performanceTest();
        collector.addTest('性能测试', perfResult.summary.successRate >= 80 ? 'PASSED' : 'FAILED', {
            message: `成功率: ${perfResult.summary.successRate}%, 平均响应时间: ${perfResult.summary.avgResponseTime}ms`,
            data: perfResult
        });

        // 7. 清理资源
        console.log('\n📋 步骤7: 清理测试资源');
        await stopLocalContainer();
        collector.addTest('资源清理', 'PASSED', {
            message: '容器已停止并清理'
        });

    } catch (error) {
        collector.addTest('测试执行', 'FAILED', {
            message: error.message,
            stack: error.stack
        });
    }

    console.log('\n' + '='.repeat(80));
    return collector.generateReport();
}

// 运行测试
if (require.main === module) {
    runEndToEndTest().catch(console.error);
}

module.exports = {
    runEndToEndTest,
    simulateCloudFunctionCall,
    testCloudRunHealth,
    testCloudRunSnapshot,
    generateResumeHTML,
    REAL_RESUME_DATA
};
