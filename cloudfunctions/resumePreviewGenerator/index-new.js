/**
 * 简历预览生成器 - 简化版本
 * 生成HTML内容和简单的预览图片
 */

'use strict';

const cloud = require('wx-server-sdk');

// 初始化云开发环境
cloud.init({
    env: 'zemuresume-4gjvx1wea78e3d1e'
});

/**
 * 生成简单的占位图片
 * @returns {Buffer} PNG图片数据
 */
function generatePlaceholderImage() {
    // 创建一个简单的PNG占位图片Buffer
    const placeholderPNG = Buffer.from([
        0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D,
        0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x03, 0x20, 0x00, 0x00, 0x04, 0x63,
        0x08, 0x02, 0x00, 0x00, 0x00, 0x8C, 0x1D, 0x87, 0x7D, 0x00, 0x00, 0x00,
        0x0C, 0x49, 0x44, 0x41, 0x54, 0x78, 0x9C, 0x63, 0xF8, 0x0F, 0x00, 0x01,
        0x01, 0x01, 0x00, 0x18, 0xDD, 0x8D, 0xB4, 0x00, 0x00, 0x00, 0x00, 0x49,
        0x45, 0x4E, 0x44, 0xAE, 0x42, 0x60, 0x82
    ]);
    
    return placeholderPNG;
}

/**
 * 生成简历HTML内容
 * @param {object} resumeData - 简历数据
 * @returns {string} HTML内容
 */
function generateResumeHTML(resumeData) {
    const name = resumeData.personalInfo?.name || '求职者';
    const email = resumeData.personalInfo?.email || '';
    const phone = resumeData.personalInfo?.phone || '';
    const summary = resumeData.summary || '';
    const workExperience = resumeData.workExperience || [];
    const education = resumeData.education || [];
    const skills = resumeData.skills || [];

    return `<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>${name} - 简历</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #fff;
            padding: 40px;
            max-width: 800px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            border-bottom: 3px solid #2c3e50;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .name {
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #2c3e50;
        }
        .contact {
            color: #7f8c8d;
            font-size: 16px;
        }
        .section {
            margin: 25px 0;
        }
        .section-title {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #2c3e50;
            border-left: 4px solid #3498db;
            padding-left: 10px;
        }
        .section-content {
            margin-left: 14px;
        }
        .work-item, .edu-item {
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
        }
        .work-title, .edu-title {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        .work-company, .edu-school {
            color: #3498db;
            margin-bottom: 5px;
        }
        .work-date, .edu-date {
            color: #7f8c8d;
            font-size: 14px;
            margin-bottom: 10px;
        }
        .skills-list {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }
        .skill-tag {
            background: #3498db;
            color: white;
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="name">${name}</div>
        <div class="contact">${phone} | ${email}</div>
    </div>

    ${summary ? `
    <div class="section">
        <div class="section-title">个人简介</div>
        <div class="section-content">${summary}</div>
    </div>
    ` : ''}

    ${workExperience.length > 0 ? `
    <div class="section">
        <div class="section-title">工作经历</div>
        <div class="section-content">
            ${workExperience.map(work => `
                <div class="work-item">
                    <div class="work-title">${work.position || '职位'}</div>
                    <div class="work-company">${work.company || '公司'}</div>
                    <div class="work-date">${work.startDate || ''} - ${work.endDate || '至今'}</div>
                    <div>${work.description || ''}</div>
                </div>
            `).join('')}
        </div>
    </div>
    ` : ''}

    ${education.length > 0 ? `
    <div class="section">
        <div class="section-title">教育背景</div>
        <div class="section-content">
            ${education.map(edu => `
                <div class="edu-item">
                    <div class="edu-title">${edu.degree || '学位'} - ${edu.major || '专业'}</div>
                    <div class="edu-school">${edu.school || '学校'}</div>
                    <div class="edu-date">${edu.startDate || ''} - ${edu.endDate || ''}</div>
                </div>
            `).join('')}
        </div>
    </div>
    ` : ''}

    ${skills.length > 0 ? `
    <div class="section">
        <div class="section-title">专业技能</div>
        <div class="section-content">
            <div class="skills-list">
                ${skills.map(skill => `<span class="skill-tag">${skill}</span>`).join('')}
            </div>
        </div>
    </div>
    ` : ''}
</body>
</html>`;
}

/**
 * 云函数入口函数
 */
exports.main = async (event, context) => {
    console.log('🚀 ResumePreviewGenerator 简化版本启动');
    console.log('📥 接收到事件:', JSON.stringify(event, null, 2));

    try {
        const { resumeData, format = 'png' } = event;

        // 参数验证
        if (!resumeData) {
            return {
                statusCode: 400,
                body: JSON.stringify({
                    success: false,
                    error: '缺少简历数据'
                })
            };
        }

        const timestamp = Date.now();
        const results = {};

        // 生成HTML内容
        console.log('📝 生成简历HTML内容...');
        const htmlContent = generateResumeHTML(resumeData);

        // 上传HTML到云存储
        const htmlFileName = `resume-html-${timestamp}.html`;
        const htmlCloudPath = `resume-html/${htmlFileName}`;

        const htmlUploadResult = await cloud.uploadFile({
            cloudPath: htmlCloudPath,
            fileContent: Buffer.from(htmlContent, 'utf8')
        });

        // 获取HTML的临时访问链接
        const htmlTempUrlResult = await cloud.getTempFileURL({
            fileList: [htmlUploadResult.fileID]
        });

        results.html = {
            url: htmlTempUrlResult.fileList[0].tempFileURL,
            fileID: htmlUploadResult.fileID,
            cloudPath: htmlCloudPath,
            content: htmlContent
        };

        console.log('📄 HTML已上传，临时URL:', results.html.url);

        // 生成PNG预览（占位图片）
        if (format === 'png' || format === 'both') {
            console.log('🖼️ 生成PNG占位图片...');

            const pngBuffer = generatePlaceholderImage();

            // 上传PNG到云存储
            const pngFileName = `resume-preview-${timestamp}.png`;
            const pngCloudPath = `resume-previews/${pngFileName}`;

            const pngUploadResult = await cloud.uploadFile({
                cloudPath: pngCloudPath,
                fileContent: pngBuffer
            });

            // 获取PNG的临时访问链接
            const pngTempUrlResult = await cloud.getTempFileURL({
                fileList: [pngUploadResult.fileID]
            });

            results.png = {
                imageUrl: pngTempUrlResult.fileList[0].tempFileURL,
                fileID: pngUploadResult.fileID,
                cloudPath: pngCloudPath,
                fileSize: pngBuffer.length,
                note: '当前为占位图片，实际预览请访问HTML链接'
            };

            console.log('✅ PNG占位图片生成成功');
        }

        return {
            statusCode: 200,
            body: JSON.stringify({
                success: true,
                data: {
                    ...results,
                    templateType: 'professional',
                    generatedAt: new Date().toISOString(),
                    mode: 'cloud-function-simple'
                }
            })
        };

    } catch (error) {
        console.error('❌ 简历预览生成失败:', error);
        return {
            statusCode: 500,
            body: JSON.stringify({
                success: false,
                error: error.message
            })
        };
    }
};
