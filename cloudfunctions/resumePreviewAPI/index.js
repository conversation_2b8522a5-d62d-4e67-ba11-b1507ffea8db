/**
 * 简历预览API网关
 * 为前端提供HTTP接口调用云函数
 */

'use strict';

const cloud = require('wx-server-sdk');

// 初始化云开发环境
cloud.init({
    env: 'zemuresume-4gjvx1wea78e3d1e'
});

/**
 * 云函数入口函数
 */
exports.main = async (event, context) => {
    console.log('🚀 ResumePreviewAPI 启动');
    console.log('📥 接收到事件:', JSON.stringify(event, null, 2));

    // 设置CORS头
    const headers = {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization'
    };

    try {
        // 处理OPTIONS预检请求
        if (event.httpMethod === 'OPTIONS') {
            return {
                statusCode: 200,
                headers: headers,
                body: JSON.stringify({ message: 'OK' })
            };
        }

        // 只处理POST请求
        if (event.httpMethod !== 'POST') {
            return {
                statusCode: 405,
                headers: headers,
                body: JSON.stringify({
                    success: false,
                    error: '只支持POST请求'
                })
            };
        }

        // 解析请求体
        let requestData;
        try {
            requestData = typeof event.body === 'string' ? JSON.parse(event.body) : event.body;
        } catch (parseError) {
            return {
                statusCode: 400,
                headers: headers,
                body: JSON.stringify({
                    success: false,
                    error: '请求体格式错误'
                })
            };
        }

        // 调用简历预览生成云函数
        console.log('📞 调用resumePreviewGenerator云函数...');
        const result = await cloud.callFunction({
            name: 'resumePreviewGenerator',
            data: requestData
        });

        console.log('✅ 云函数调用成功');

        // 解析云函数返回结果
        let functionResult;
        try {
            functionResult = typeof result.result === 'string' ? JSON.parse(result.result) : result.result;
        } catch (parseError) {
            functionResult = result.result;
        }

        // 如果云函数返回的是包装格式，需要解析body
        if (functionResult.statusCode && functionResult.body) {
            const bodyData = typeof functionResult.body === 'string' ? JSON.parse(functionResult.body) : functionResult.body;
            return {
                statusCode: functionResult.statusCode,
                headers: headers,
                body: JSON.stringify(bodyData)
            };
        }

        // 直接返回云函数结果
        return {
            statusCode: 200,
            headers: headers,
            body: JSON.stringify(functionResult)
        };

    } catch (error) {
        console.error('❌ API处理失败:', error);
        return {
            statusCode: 500,
            headers: headers,
            body: JSON.stringify({
                success: false,
                error: error.message || '服务器内部错误'
            })
        };
    }
};
