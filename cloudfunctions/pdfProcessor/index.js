/**
 * PDF Processor - PDF处理云函数
 * 处理PDF文件的上传、解析和生成
 */

'use strict';

const cloud = require('wx-server-sdk');
const tencentcloud = require('tencentcloud-sdk-nodejs');

// 初始化云开发
cloud.init({
  env: 'zemuresume-4gjvx1wea78e3d1e'
});

const db = cloud.database();

// 初始化腾讯云OCR客户端
const OcrClient = tencentcloud.ocr.v20181119.Client;

// 从环境变量获取腾讯云密钥
const clientConfig = {
  credential: {
    secretId: process.env.TENCENT_SECRET_ID || 'YOUR_SECRET_ID',
    secretKey: process.env.TENCENT_SECRET_KEY || 'YOUR_SECRET_KEY',
  },
  region: 'ap-guangzhou',
  profile: {
    httpProfile: {
      endpoint: 'ocr.tencentcloudapi.com',
    },
  },
};

const ocrClient = new OcrClient(clientConfig);

/**
 * 主处理函数
 */
exports.main = async (event, context) => {
  const startTime = Date.now();
  console.log('🚀 PdfProcessor 开始处理请求', { event, context });

  try {
    // 解析输入数据
    let requestData = {};
    if (typeof event === 'string') {
      requestData = JSON.parse(event);
    } else if (event.body) {
      requestData = typeof event.body === 'string' ? JSON.parse(event.body) : event.body;
    } else {
      requestData = event;
    }

    const { action, data } = requestData;

    if (!action) {
      return createErrorResponse('缺少action参数');
    }

    let result;

    switch (action) {
      case 'upload-pdf':
        result = await uploadPDF(data);
        break;
      case 'parse-pdf':
        result = await parsePDF(data);
        break;
      case 'ocr-pdf':
        result = await parsePDF(data);  // OCR解析使用相同的parsePDF函数
        break;
      case 'generate-pdf':
        result = await generatePDF(data);
        break;
      case 'download-pdf':
        result = await downloadPDF(data);
        break;
      default:
        return createErrorResponse(`不支持的action: ${action}`);
    }

    const processingTime = Date.now() - startTime;
    console.log('✅ PDF处理完成', { action, processingTime });

    return createSuccessResponse({
      success: true,
      data: result,
      action: action,
      processingTime
    });

  } catch (error) {
    const processingTime = Date.now() - startTime;
    console.error('❌ PDF处理失败:', error);

    return createErrorResponse(error.message, {
      processingTime,
      timestamp: new Date().toISOString()
    });
  }
};

/**
 * 上传PDF文件
 */
async function uploadPDF(data) {
  try {
    const { fileBuffer, fileName, userId } = data;

    if (!fileBuffer || !fileName) {
      throw new Error('缺少文件数据或文件名');
    }

    console.log('📤 开始上传PDF文件', { fileName, userId });

    // 生成文件路径
    const timestamp = Date.now();
    const cloudPath = `resumes/${userId || 'anonymous'}/${timestamp}_${fileName}`;

    // 上传到云存储
    const uploadResult = await cloud.uploadFile({
      cloudPath: cloudPath,
      fileContent: Buffer.from(fileBuffer, 'base64')
    });

    // 保存文件记录到数据库
    const fileRecord = {
      fileId: uploadResult.fileID,
      fileName: fileName,
      cloudPath: cloudPath,
      userId: userId || null,
      uploadTime: new Date(),
      fileType: 'pdf',
      status: 'uploaded'
    };

    await db.collection('files').add({
      data: fileRecord
    });

    return {
      fileId: uploadResult.fileID,
      cloudPath: cloudPath,
      fileName: fileName,
      uploadTime: fileRecord.uploadTime
    };

  } catch (error) {
    console.error('❌ PDF上传失败:', error);
    throw new Error(`PDF上传失败: ${error.message}`);
  }
}

/**
 * 解析PDF文件
 */
async function parsePDF(data) {
  try {
    const { fileId, cloudPath, fileName } = data;

    if (!fileId && !cloudPath) {
      throw new Error('缺少文件ID或云路径');
    }

    console.log('📄 开始解析PDF文件', { fileId, cloudPath, fileName });

    // 从云存储下载文件
    let downloadResult;
    if (fileId) {
      downloadResult = await cloud.downloadFile({
        fileID: fileId
      });
    } else if (cloudPath) {
      // 如果是cloudPath，需要构造完整的fileID
      const env = cloud.DYNAMIC_CURRENT_ENV || 'zemuresume-4gjvx1wea78e3d1e';
      const fullFileId = `cloud://zemuresume-4gjvx1wea78e3d1e.7a65-zemuresume-4gjvx1wea78e3d1e-1341667342/${cloudPath}`;
      downloadResult = await cloud.downloadFile({
        fileID: fullFileId
      });
    } else {
      throw new Error('缺少文件ID或云路径');
    }

    console.log('📥 文件下载完成，开始OCR识别');

    // 使用腾讯云OCR进行文本识别
    const parsedContent = await performOCRRecognition(downloadResult.fileContent, fileName);

    // 优化返回内容，避免传输问题
    const contentLength = parsedContent.length;
    console.log('📊 OCR内容统计:', {
      contentLength,
      contentPreview: parsedContent.substring(0, 200) + '...'
    });

    // 如果内容过长，进行适当的压缩处理
    let optimizedContent = parsedContent;
    if (contentLength > 10000) {
      console.log('⚠️ 内容较长，进行优化处理');
      // 保持原始内容，但添加统计信息
      optimizedContent = parsedContent;
    }

    return {
      content: optimizedContent,
      fileId: fileId,
      fileName: fileName,
      parseTime: new Date(),
      method: 'tencent-ocr',
      contentStats: {
        totalLength: contentLength,
        pages: (parsedContent.match(/--- 第\d+页 ---/g) || []).length || 1,
        estimatedWords: Math.round(contentLength / 2)
      }
    };

  } catch (error) {
    console.error('❌ PDF解析失败:', error);
    throw new Error(`PDF解析失败: ${error.message}`);
  }
}

/**
 * 生成PDF文件
 */
async function generatePDF(data) {
  try {
    const { resumeData, templateId, userId } = data;

    if (!resumeData) {
      throw new Error('缺少简历数据');
    }

    console.log('📝 开始生成PDF文件', { templateId, userId });

    // 这里应该使用PDF生成库，暂时返回模拟数据
    const pdfBuffer = await mockPDFGeneration(resumeData, templateId);

    // 生成文件路径
    const timestamp = Date.now();
    const fileName = `generated-resume-${timestamp}.pdf`;
    const cloudPath = `generated/${userId || 'anonymous'}/${fileName}`;

    // 上传生成的PDF到云存储
    const uploadResult = await cloud.uploadFile({
      cloudPath: cloudPath,
      fileContent: pdfBuffer
    });

    // 保存文件记录到数据库
    const fileRecord = {
      fileId: uploadResult.fileID,
      fileName: fileName,
      cloudPath: cloudPath,
      userId: userId || null,
      generateTime: new Date(),
      fileType: 'pdf',
      status: 'generated',
      templateId: templateId || 'default'
    };

    await db.collection('files').add({
      data: fileRecord
    });

    return {
      fileId: uploadResult.fileID,
      cloudPath: cloudPath,
      fileName: fileName,
      downloadUrl: uploadResult.fileID,
      generateTime: fileRecord.generateTime
    };

  } catch (error) {
    console.error('❌ PDF生成失败:', error);
    throw new Error(`PDF生成失败: ${error.message}`);
  }
}

/**
 * 下载PDF文件
 */
async function downloadPDF(data) {
  try {
    const { fileId } = data;

    if (!fileId) {
      throw new Error('缺少文件ID');
    }

    console.log('📥 开始下载PDF文件', { fileId });

    // 获取临时下载链接
    const downloadResult = await cloud.getTempFileURL({
      fileList: [fileId]
    });

    if (downloadResult.fileList && downloadResult.fileList.length > 0) {
      const fileInfo = downloadResult.fileList[0];
      return {
        downloadUrl: fileInfo.tempFileURL,
        fileId: fileId,
        expireTime: new Date(Date.now() + 2 * 60 * 60 * 1000) // 2小时后过期
      };
    } else {
      throw new Error('获取下载链接失败');
    }

  } catch (error) {
    console.error('❌ PDF下载失败:', error);
    throw new Error(`PDF下载失败: ${error.message}`);
  }
}

/**
 * 使用腾讯云OCR进行PDF文本识别 - 支持多页PDF
 */
async function performOCRRecognition(fileContent, fileName) {
  try {
    console.log('🔍 开始腾讯云OCR识别', { fileName, fileSize: fileContent.length });

    // 将文件内容转换为Base64
    const base64Content = fileContent.toString('base64');

    // 首先检测PDF页数
    const pageCount = await detectPDFPageCount(base64Content);
    console.log(`📄 检测到PDF页数: ${pageCount}`);

    // 存储所有页面的OCR结果
    const allPageTexts = [];
    let totalDetections = 0;

    // 循环处理每一页
    for (let pageNum = 1; pageNum <= pageCount; pageNum++) {
      console.log(`🔍 正在处理第 ${pageNum}/${pageCount} 页...`);

      try {
        // 调用腾讯云通用印刷体识别API
        const params = {
          ImageBase64: base64Content,
          LanguageType: 'zh',  // 中英文混合
          IsPdf: true,         // 指定为PDF文件
          PdfPageNumber: pageNum
        };

        const response = await ocrClient.GeneralBasicOCR(params);

        if (response.TextDetections && response.TextDetections.length > 0) {
          // 提取识别到的文本
          const pageText = response.TextDetections
            .map(detection => detection.DetectedText)
            .join('\n');

          allPageTexts.push(`--- 第${pageNum}页 ---\n${pageText}`);
          totalDetections += response.TextDetections.length;

          console.log(`✅ 第${pageNum}页OCR识别成功`, {
            detectionsCount: response.TextDetections.length,
            textLength: pageText.length
          });
        } else {
          console.warn(`⚠️ 第${pageNum}页未识别到文本内容`);
          allPageTexts.push(`--- 第${pageNum}页 ---\n(未识别到文本内容)`);
        }

        // 添加延迟避免API限流
        if (pageNum < pageCount) {
          await new Promise(resolve => setTimeout(resolve, 100));
        }

      } catch (pageError) {
        console.error(`❌ 第${pageNum}页OCR识别失败:`, pageError);
        allPageTexts.push(`--- 第${pageNum}页 ---\n(OCR识别失败: ${pageError.message})`);
      }
    }

    // 合并所有页面的文本
    const finalText = allPageTexts.join('\n\n');

    console.log('✅ 多页PDF OCR识别完成', {
      totalPages: pageCount,
      totalTextLength: finalText.length,
      totalDetections: totalDetections
    });

    return finalText;

  } catch (error) {
    console.error('❌ OCR识别失败:', error);

    // OCR失败时返回错误信息，但不中断流程
    return `OCR识别失败: ${error.message}。请检查文件格式或网络连接。`;
  }
}

/**
 * 检测PDF页数
 */
async function detectPDFPageCount(base64Content) {
  try {
    // 使用腾讯云OCR API检测页数
    // 先尝试识别第1页，如果成功则继续尝试更多页
    let pageCount = 1;
    const maxPages = 20; // 设置最大页数限制，避免无限循环

    // 简单的页数检测：尝试识别每一页直到失败
    for (let testPage = 2; testPage <= maxPages; testPage++) {
      try {
        const testParams = {
          ImageBase64: base64Content,
          LanguageType: 'zh',
          IsPdf: true,
          PdfPageNumber: testPage
        };

        const testResponse = await ocrClient.GeneralBasicOCR(testParams);

        // 如果能成功识别，说明这一页存在
        if (testResponse.TextDetections) {
          pageCount = testPage;
          console.log(`📄 检测到第${testPage}页存在`);
        } else {
          // 如果没有检测到内容，可能是页面为空，但页面可能存在
          pageCount = testPage;
        }

        // 添加延迟避免API限流
        await new Promise(resolve => setTimeout(resolve, 50));

      } catch (error) {
        // 如果出现错误，可能是页面不存在，停止检测
        if (error.code === 'InvalidParameter.InvalidParameter' ||
          error.message.includes('page') ||
          error.message.includes('页')) {
          console.log(`📄 第${testPage}页不存在，总页数为${pageCount}`);
          break;
        } else {
          // 其他错误，继续尝试
          console.warn(`⚠️ 检测第${testPage}页时出现错误:`, error.message);
        }
      }
    }

    return Math.max(1, pageCount); // 至少返回1页

  } catch (error) {
    console.warn('⚠️ 页数检测失败，默认处理1页:', error.message);
    return 1; // 默认返回1页
  }
}

/**
 * 模拟PDF解析（备用方案）
 */
async function mockPDFParsing(fileContent) {
  // 模拟解析延迟
  await new Promise(resolve => setTimeout(resolve, 100));

  return `这是从PDF文件中解析出的文本内容。

个人信息：
姓名：张三
邮箱：<EMAIL>
电话：13800138000

工作经历：
2020-2023 软件工程师 - 科技公司
- 负责前端开发
- 参与项目架构设计

教育背景：
2016-2020 计算机科学学士 - 某大学

技能：
JavaScript, React, Node.js`;
}

/**
 * 模拟PDF生成（实际应使用pdf-lib等库）
 */
async function mockPDFGeneration(resumeData, templateId) {
  // 模拟生成延迟
  await new Promise(resolve => setTimeout(resolve, 200));

  // 返回模拟的PDF二进制数据
  const mockPDFContent = `%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj

2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj

3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
>>
endobj

xref
0 4
0000000000 65535 f 
0000000009 00000 n 
0000000058 00000 n 
0000000115 00000 n 
trailer
<<
/Size 4
/Root 1 0 R
>>
startxref
174
%%EOF`;

  return Buffer.from(mockPDFContent);
}

/**
 * 创建成功响应
 */
function createSuccessResponse(data, metadata = {}) {
  return {
    statusCode: 200,
    headers: {
      'Content-Type': 'application/json',
      'Access-Control-Allow-Origin': '*'
    },
    body: JSON.stringify({
      success: true,
      data: data,
      metadata: {
        timestamp: new Date().toISOString(),
        environment: 'zemuresume-4gjvx1wea78e3d1e',
        ...metadata
      }
    })
  };
}

/**
 * 创建错误响应
 */
function createErrorResponse(message, metadata = {}) {
  return {
    statusCode: 500,
    headers: {
      'Content-Type': 'application/json',
      'Access-Control-Allow-Origin': '*'
    },
    body: JSON.stringify({
      success: false,
      error: message,
      metadata: {
        timestamp: new Date().toISOString(),
        environment: 'zemuresume-4gjvx1wea78e3d1e',
        ...metadata
      }
    })
  };
}
