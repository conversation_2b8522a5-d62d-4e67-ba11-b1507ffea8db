#!/usr/bin/env python3
"""
部署修复后的ai-resume-user-login SCF函数
"""

import os
import json
import zipfile
import tempfile
import shutil
from tencentcloud.common import credential
from tencentcloud.common.profile.client_profile import ClientProfile
from tencentcloud.common.profile.http_profile import HttpProfile
from tencentcloud.scf.v20180416 import scf_client, models

# 腾讯云配置
SECRET_ID = "YOUR_SECRET_ID"
SECRET_KEY = "YOUR_SECRET_KEY"
REGION = "ap-guangzhou"
FUNCTION_NAME = "ai-resume-user-login"

# 函数配置
FUNCTION_CONFIG = {
    "FunctionName": FUNCTION_NAME,
    "Description": "AI Resume 用户登录认证服务 - 修复GET请求500错误",
    "Handler": "index.main_handler",
    "Runtime": "Nodejs18.15",
    "MemorySize": 128,
    "Timeout": 30,
    "Environment": {
        "Variables": {
            "WECHAT_APPID": "wx4fa04593aaf3bb76",
            "WECHAT_SECRET": "4a90dd7e501e24f0489e2b83031e1100",
            "JWT_SECRET": "ai-resume-jwt-secret-key-2024-wx4fa04593aaf3bb76",
            "JWT_EXPIRES_IN": "7d",
            "NODE_ENV": "production"
        }
    }
}

def init_scf_client():
    """初始化SCF客户端"""
    cred = credential.Credential(SECRET_ID, SECRET_KEY)
    
    httpProfile = HttpProfile()
    httpProfile.endpoint = "scf.tencentcloudapi.com"
    
    clientProfile = ClientProfile()
    clientProfile.httpProfile = httpProfile
    
    return scf_client.ScfClient(cred, REGION, clientProfile)

def create_deployment_package():
    """创建部署包"""
    print("📦 创建部署包...")
    
    # 创建临时目录
    temp_dir = tempfile.mkdtemp()
    function_dir = os.path.join(temp_dir, "function")
    os.makedirs(function_dir)
    
    try:
        # 复制函数文件
        source_dir = "auth-functions/user-login"
        
        # 复制index.js
        shutil.copy2(
            os.path.join(source_dir, "index.js"),
            os.path.join(function_dir, "index.js")
        )
        
        # 复制package.json
        shutil.copy2(
            os.path.join(source_dir, "package.json"),
            os.path.join(function_dir, "package.json")
        )
        
        # 创建zip文件
        zip_path = os.path.join(temp_dir, "function.zip")
        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for root, dirs, files in os.walk(function_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    arcname = os.path.relpath(file_path, function_dir)
                    zipf.write(file_path, arcname)
        
        print(f"✅ 部署包创建成功: {zip_path}")
        return zip_path
        
    except Exception as e:
        print(f"❌ 创建部署包失败: {e}")
        shutil.rmtree(temp_dir)
        return None

def deploy_function(client, zip_path):
    """部署函数"""
    print("🚀 开始部署函数...")
    
    try:
        # 读取zip文件并进行base64编码
        import base64
        with open(zip_path, 'rb') as f:
            zip_content = f.read()

        # 更新函数代码
        req = models.UpdateFunctionCodeRequest()
        req.FunctionName = FUNCTION_NAME
        req.ZipFile = base64.b64encode(zip_content).decode('utf-8')
        
        resp = client.UpdateFunctionCode(req)
        print(f"✅ 函数代码更新成功")
        
        # 更新函数配置
        config_req = models.UpdateFunctionConfigurationRequest()
        config_req.FunctionName = FUNCTION_NAME
        config_req.Description = FUNCTION_CONFIG["Description"]
        config_req.MemorySize = FUNCTION_CONFIG["MemorySize"]
        config_req.Timeout = FUNCTION_CONFIG["Timeout"]

        # 设置环境变量
        env_vars = []
        for k, v in FUNCTION_CONFIG["Environment"]["Variables"].items():
            var = models.Variable()
            var.Key = k
            var.Value = v
            env_vars.append(var)

        config_req.Environment = models.Environment()
        config_req.Environment.Variables = env_vars
        
        config_resp = client.UpdateFunctionConfiguration(config_req)
        print(f"✅ 函数配置更新成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 部署函数失败: {e}")
        return False

def test_deployed_function():
    """测试部署后的函数"""
    print("🧪 测试部署后的函数...")
    
    import requests
    import time
    
    # 等待函数部署完成
    print("⏳ 等待函数部署完成...")
    time.sleep(10)
    
    scf_url = "http://1341667342-l7dh36l4tt.ap-guangzhou.tencentscf.com"
    
    # 测试GET请求
    print(f"📡 测试GET请求: {scf_url}")
    try:
        response = requests.get(scf_url, timeout=10)
        print(f"   状态码: {response.status_code}")
        print(f"   响应内容: {response.text}")
        
        if response.status_code == 200:
            print("✅ GET请求测试成功 - 500错误已修复！")
            return True
        else:
            print(f"❌ GET请求仍然失败，状态码: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ GET请求测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 开始部署修复后的SCF函数...")
    print("=" * 60)
    
    # 初始化客户端
    client = init_scf_client()
    
    # 创建部署包
    zip_path = create_deployment_package()
    if not zip_path:
        return False
    
    try:
        # 部署函数
        if deploy_function(client, zip_path):
            print("\n" + "=" * 60)
            # 测试函数
            success = test_deployed_function()
            
            if success:
                print("\n🎉 函数部署和测试成功！")
                print("✅ GET请求500错误已修复")
                print("✅ 函数现在可以正常响应GET和POST请求")
            else:
                print("\n⚠️ 函数部署成功但测试失败")
            
            return success
        else:
            return False
            
    finally:
        # 清理临时文件
        if zip_path and os.path.exists(zip_path):
            os.remove(zip_path)
            print("🧹 清理临时文件完成")

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
