# 部署后云函数更新指南

## 📋 概述

微信云托管服务部署成功后，需要更新云函数中的服务地址，以便云函数能够正确调用截图服务。

## 🔧 更新步骤

### 第1步：获取微信云托管服务地址

部署成功后，您的服务地址格式为：
```
https://ai-resume-snapshot-prod-2gczdho212bd52ca.ap-shanghai.run.tcloudbase.com
```

**获取方式**：
1. 访问微信云托管控制台
2. 进入服务详情页
3. 复制"访问地址"

### 第2步：更新云函数代码

需要修改 `cloudfunctions/resumePreviewGenerator/index.js` 文件中的服务地址：

**查找这一行**：
```javascript
const SNAPSHOT_SERVICE_URL = 'https://resume-snapshot-zemuresume-4gjvx1wea78e3d1e.ap-shanghai.run.tcloudbase.com';
```

**替换为实际地址**：
```javascript
const SNAPSHOT_SERVICE_URL = 'https://ai-resume-snapshot-prod-2gczdho212bd52ca.ap-shanghai.run.tcloudbase.com';
```

### 第3步：重新部署云函数

使用CloudBase CLI重新部署：
```bash
cd /Users/<USER>/Desktop/ai-resume
cloudbase functions:deploy resumePreviewGenerator
```

或者通过CloudBase控制台手动上传更新。

## 🧪 测试验证

### 测试云函数
```javascript
// 在CloudBase控制台测试
{
  "resumeData": {
    "personalInfo": {
      "name": "测试用户",
      "email": "<EMAIL>",
      "phone": "13800138000"
    },
    "summary": "这是一个测试简历",
    "workExperience": [
      {
        "position": "测试工程师",
        "company": "测试公司",
        "startDate": "2023-01",
        "endDate": "至今",
        "description": "负责系统测试工作"
      }
    ],
    "skills": ["测试技能1", "测试技能2"]
  },
  "format": "png"
}
```

### 预期结果
- ✅ HTML生成成功
- ✅ 微信云托管调用成功
- ✅ PNG截图生成成功
- ✅ 返回真实的预览图片URL

## 📊 完整的系统架构

```
用户请求 
    ↓
resumePreviewGenerator云函数
    ↓
生成HTML简历
    ↓
上传到云存储
    ↓
调用微信云托管截图服务
    ↓
生成PNG预览图
    ↓
上传PNG到云存储
    ↓
返回HTML和PNG的访问链接
```

## 🎯 性能指标

更新后的系统应达到以下性能指标：
- **HTML生成时间**: < 2秒
- **PNG截图生成时间**: < 8秒
- **总响应时间**: < 10秒
- **功能成功率**: ≥ 95%

## 🔗 管理链接

- **CloudBase云函数控制台**: https://tcb.cloud.tencent.com/dev?envId=zemuresume-4gjvx1wea78e3d1e#/scf/detail?id=resumePreviewGenerator&NameSpace=zemuresume-4gjvx1wea78e3d1e
- **微信云托管控制台**: https://cloud.weixin.qq.com/cloudrun/service/detail?envId=prod-2gczdho212bd52ca&serviceName=ai-resume-snapshot

## ⚠️ 注意事项

1. **服务地址更新**：确保使用实际部署后的服务地址
2. **网络连通性**：确认云函数能够访问微信云托管服务
3. **错误处理**：系统已内置降级机制，如果微信云托管不可用会自动使用占位图片
4. **监控告警**：建议设置监控告警，及时发现服务异常

---

**更新完成后，AI简历系统将具备完整的真实PNG截图生成能力！** 🎉
