name: Deploy to WeChat CloudRun

on:
  push:
    branches: [ main, master ]
  pull_request:
    branches: [ main, master ]

jobs:
  deploy:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Run tests
      run: |
        npm run lint || echo "No lint script found"
        npm test || echo "No test script found"
        
    - name: Install WeChat CloudRun CLI
      run: |
        npm install -g @cloudbase/cli@latest
        
    - name: Deploy to WeChat CloudRun
      env:
        CLOUDBASE_SECRET_ID: ${{ secrets.CLOUDBASE_SECRET_ID }}
        CLOUDBASE_SECRET_KEY: ${{ secrets.CLOUDBASE_SECRET_KEY }}
        CLOUDBASE_ENV_ID: ${{ secrets.CLOUDBASE_ENV_ID }}
      run: |
        echo "配置云开发环境: $CLOUDBASE_ENV_ID"
        
        # 使用微信云托管CLI部署
        npx @cloudbase/cli login --key
        npx @cloudbase/cli framework deploy --verbose
        
    - name: Health Check
      run: |
        sleep 30
        
        HEALTH_URL="https://ai-resume-snapshot2-176277-5-**********.sh.run.tcloudbase.com/health"
        echo "检查服务健康状态: $HEALTH_URL"
        
        for i in {1..5}; do
          if curl -f -s "$HEALTH_URL"; then
            echo "✅ 服务健康检查通过"
            break
          else
            echo "⏳ 等待服务启动... (尝试 $i/5)"
            sleep 10
          fi
        done
        
    - name: Test Screenshot Service
      run: |
        SNAPSHOT_URL="https://ai-resume-snapshot2-176277-5-**********.sh.run.tcloudbase.com/snapshot"
        echo "测试截图服务: $SNAPSHOT_URL"
        
        curl -X POST "$SNAPSHOT_URL" \
          -H "Content-Type: application/json" \
          -d '{"url":"https://example.com","format":"png","width":800,"height":600}' \
          --max-time 30 \
          --output test-screenshot.png || echo "截图服务测试失败，但不阻止部署"
          
    - name: Notify Deployment Result
      if: always()
      run: |
        if [ "${{ job.status }}" == "success" ]; then
          echo "🎉 部署成功！"
          echo "📱 微信云托管服务已更新"
          echo "🔗 健康检查: https://ai-resume-snapshot2-176277-5-**********.sh.run.tcloudbase.com/health"
          echo "📸 截图服务: https://ai-resume-snapshot2-176277-5-**********.sh.run.tcloudbase.com/snapshot"
        else
          echo "❌ 部署失败，请检查日志"
        fi
