#!/usr/bin/env python3
"""
SCF函数Python紧急修复脚本
解决tccli JSON格式问题，直接使用腾讯云SDK修复配置
"""

import json
import time
import requests
from tencentcloud.common import credential
from tencentcloud.common.profile.client_profile import ClientProfile
from tencentcloud.common.profile.http_profile import HttpProfile
from tencentcloud.scf.v20180416 import scf_client, models

print("🚨 SCF函数Python紧急修复开始...")
print("🎯 当前成功率：25% → 目标：≥95%")
print("🔧 使用Python SDK直接修复配置")

# 腾讯云认证配置
SECRET_ID = "YOUR_SECRET_ID"
SECRET_KEY = "YOUR_SECRET_KEY"

def init_scf_client():
    """初始化SCF客户端"""
    try:
        cred = credential.Credential(SECRET_ID, SECRET_KEY)
        httpProfile = HttpProfile()
        httpProfile.endpoint = "scf.ap-guangzhou.tencentcloudapi.com"

        clientProfile = ClientProfile()
        clientProfile.httpProfile = httpProfile

        client = scf_client.ScfClient(cred, "ap-guangzhou", clientProfile)
        return client
    except Exception as e:
        print(f"❌ 初始化SCF客户端失败: {e}")
        return None

def update_function_env(client, function_name, env_vars):
    """更新函数环境变量"""
    try:
        req = models.UpdateFunctionConfigurationRequest()
        params = {
            "FunctionName": function_name,
            "Environment": {
                "Variables": env_vars
            }
        }
        req.from_json_string(json.dumps(params))

        resp = client.UpdateFunctionConfiguration(req)
        print(f"✅ {function_name} 环境变量更新成功")
        return True
    except Exception as e:
        print(f"❌ {function_name} 环境变量更新失败: {e}")
        return False

def test_function_connectivity(url, name):
    """测试函数连通性"""
    try:
        response = requests.get(url, timeout=10)
        status_code = response.status_code

        if status_code in [200, 404, 405]:
            print(f"   ✅ {name} 连通性正常 (HTTP {status_code})")
            return True
        else:
            print(f"   ❌ {name} 连通性异常 (HTTP {status_code})")
            return False
    except Exception as e:
        print(f"   ❌ {name} 连通性测试失败: {e}")
        return False

def main():
    """主修复函数"""
    client = init_scf_client()
    if not client:
        print("❌ 无法初始化SCF客户端，请检查认证配置")
        return

    print("\n🔧 开始修复函数配置...")

    # 函数配置映射
    function_configs = {
        "ai-resume-main-api": {
            "USER_LOGIN_FUNCTION_URL": "http://1341667342-l7dh36l4tt.ap-guangzhou.tencentscf.com",
            "TOKEN_VERIFY_FUNCTION_URL": "http://1341667342-ldcw844wvx.ap-guangzhou.tencentscf.com",
            "WECHAT_APPID": "wx2309e473610ea429",
            "WECHAT_SECRET": "26ca850faf0fafcbad3cea9ce8ac9786",
            "JWT_SECRET": "ai-resume-jwt-secret-key-2024-wx2309e473610ea429",
            "NODE_ENV": "production"
        },
        "ai-resume-token-verify": {
            "JWT_SECRET": "ai-resume-jwt-secret-key-2024-wx2309e473610ea429",
            "NODE_ENV": "production",
            "WECHAT_APPID": "wx2309e473610ea429"
        },
        "ai-resume-user-login": {
            "WECHAT_APPID": "wx2309e473610ea429",
            "WECHAT_SECRET": "26ca850faf0fafcbad3cea9ce8ac9786",
            "JWT_SECRET": "ai-resume-jwt-secret-key-2024-wx2309e473610ea429",
            "JWT_EXPIRES_IN": "7d",
            "NODE_ENV": "production"
        },
        "resumeWorker": {
            "NODE_ENV": "production",
            "CLOUDBASE_ENV_ID": "cloud1-7gre7bf9deb695ed",
            "OPENROUTER_API_KEY": "YOUR_OPENROUTER_API_KEY",
            "REGION": "ap-guangzhou"
        },
        "gateway": {
            "NODE_ENV": "production",
            "REGION": "ap-guangzhou",
            "TENCENT_SECRET_ID": "YOUR_SECRET_ID",
            "TENCENT_SECRET_KEY": "YOUR_SECRET_KEY"
        },
        "jdWorker": {
            "NODE_ENV": "production",
            "CLOUDBASE_ENV_ID": "cloud1-7gre7bf9deb695ed",
            "CLOUDBASE_SECRET_ID": "YOUR_SECRET_ID",
            "CLOUDBASE_SECRET_KEY": "YOUR_SECRET_KEY",
            "OPENROUTER_API_KEY": "YOUR_OPENROUTER_API_KEY",
            "REGION": "ap-guangzhou"
        },
        "cvGenerator": {
            "NODE_ENV": "production",
            "OPENROUTER_API_KEY": "YOUR_OPENROUTER_API_KEY",
            "REGION": "ap-guangzhou"
        }
    }

    # 逐个修复函数配置
    success_count = 0
    for function_name, env_vars in function_configs.items():
        print(f"\n🔧 修复 {function_name}...")
        if update_function_env(client, function_name, env_vars):
            success_count += 1

    print(f"\n📊 配置更新结果: {success_count}/{len(function_configs)} 成功")

    # 等待配置生效
    print("\n⏳ 等待配置生效...")
    time.sleep(30)

    # 测试连通性
    print("\n🧪 测试函数连通性...")

    function_urls = {
        "ai-resume-main-api": "http://1341667342-cl17z1nljt.ap-guangzhou.tencentscf.com",
        "resumeWorker": "http://1341667342-21fo417pqr.ap-guangzhou.tencentscf.com",
        "ai-resume-token-verify": "http://1341667342-ldcw844wvx.ap-guangzhou.tencentscf.com",
        "gateway": "http://1341667342-digl3p9k4d.ap-guangzhou.tencentscf.com",
        "jdWorker": "http://1341667342-4p9m4skdlh.ap-guangzhou.tencentscf.com",
        "ai-resume-user-login": "http://1341667342-l7dh36l4tt.ap-guangzhou.tencentscf.com",
        "cvGenerator": "http://1341667342-jcyribeze9.ap-guangzhou.tencentscf.com"
    }

    connectivity_success = 0
    for name, url in function_urls.items():
        if test_function_connectivity(url, name):
            connectivity_success += 1

    success_rate = (connectivity_success / len(function_urls)) * 100

    print(f"\n📊 最终结果:")
    print(f"   连通性成功: {connectivity_success}/{len(function_urls)}")
    print(f"   成功率: {success_rate:.1f}%")

    if success_rate >= 95:
        print("🎉 修复成功！成功率达到目标 (≥95%)")
    else:
        print("⚠️  修复部分成功，需要进一步优化")
        print("\n🔗 建议:")
        print("1. 检查腾讯云控制台函数配置")
        print("2. 验证API密钥和权限设置")
        print("3. 查看CLS日志获取详细错误信息")

if __name__ == "__main__":
    main()