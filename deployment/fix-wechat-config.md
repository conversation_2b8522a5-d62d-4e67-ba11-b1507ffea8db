# 🔧 微信登录配置修复指南

## 📋 问题诊断
当前错误：`invalid appid, rid: 6873cd5e-4e4367cb-3bc981e1`

**根本原因**：`ai-resume-user-login` 函数缺少正确的微信小程序Secret配置

## 🎯 解决方案

### 步骤1：获取微信小程序Secret
1. 登录 [微信公众平台](https://mp.weixin.qq.com)
2. 选择您的小程序（AppID: `wx2309e473610ea429`）
3. 进入：开发 → 开发管理 → 开发设置
4. 找到"AppSecret"，点击"重置"或"查看"
5. 复制Secret（格式类似：`1a2b3c4d5e6f7g8h9i0j1k2l3m4n5o6p`）

### 步骤2：配置SCF函数环境变量

#### 方法A：腾讯云控制台配置（推荐）
1. 登录 [腾讯云控制台](https://console.cloud.tencent.com)
2. 进入：云函数 SCF → 函数服务
3. 找到 `ai-resume-user-login` 函数
4. 点击函数名进入详情页
5. 配置 → 环境变量 → 编辑
6. 添加/修改以下环境变量：

```
WECHAT_APPID = wx2309e473610ea429
WECHAT_SECRET = 【您从微信公众平台获取的Secret】
JWT_SECRET = ai-resume-jwt-secret-key-2024-wx2309e473610ea429
JWT_EXPIRES_IN = 7d
NODE_ENV = production
```

7. 点击"保存"

#### 方法B：使用腾讯云CLI（高级用户）
```bash
# 安装腾讯云CLI
pip install tccli

# 配置认证
tccli configure set secretId YOUR_SECRET_ID
tccli configure set secretKey 【您的SecretKey】
tccli configure set region ap-guangzhou

# 更新函数环境变量
tccli scf UpdateFunctionConfiguration \
  --region ap-guangzhou \
  --FunctionName ai-resume-user-login \
  --Environment '{
    "Variables": {
      "WECHAT_APPID": "wx2309e473610ea429",
      "WECHAT_SECRET": "【您的微信Secret】",
      "JWT_SECRET": "ai-resume-jwt-secret-key-2024-wx2309e473610ea429",
      "JWT_EXPIRES_IN": "7d",
      "NODE_ENV": "production"
    }
  }'
```

### 步骤3：验证配置
配置完成后，重新测试登录功能：

1. 在微信开发者工具中重新编译小程序
2. 点击登录按钮
3. 应该看到成功的登录响应，而不是"invalid appid"错误

## 🔍 配置验证清单

- [ ] 微信小程序AppID正确：`wx2309e473610ea429`
- [ ] 微信小程序Secret已从公众平台获取
- [ ] `ai-resume-user-login`函数环境变量已配置
- [ ] 函数配置已保存并生效
- [ ] 小程序重新编译测试

## 📞 如果仍有问题

如果配置后仍有错误，请检查：

1. **Secret是否正确**：确保从微信公众平台复制的Secret没有多余空格
2. **AppID是否匹配**：确保小程序项目使用的AppID与配置的一致
3. **函数是否重启**：修改环境变量后函数会自动重启，等待1-2分钟
4. **网络域名**：确保小程序的request合法域名包含SCF函数URL

## 💡 预期结果

配置正确后，登录应该返回类似以下的成功响应：
```json
{
  "success": true,
  "message": "登录成功",
  "data": {
    "userId": "user_xxx",
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "sessionToken": "session_xxx",
    "expiresIn": 604800,
    "userInfo": {
      "nickName": "用户昵称",
      "avatarUrl": "头像URL"
    }
  }
}
```
