# AI Resume SCF 函数部署配置
# 从 utils/各种权限密码 文件中提取的配置信息

# 腾讯云认证信息
TENCENT_SECRET_ID=YOUR_SECRET_ID
TENCENT_SECRET_KEY=YOUR_SECRET_KEY
TENCENT_REGION=ap-guangzhou

# 微信小程序配置
WECHAT_APPID=wx2309e473610ea429
WECHAT_SECRET=请在微信公众平台获取Secret

# JWT 配置
JWT_SECRET=ai-resume-jwt-secret-key-2024-wx2309e473610ea429
JWT_EXPIRES_IN=7d

# 云开发环境
CLOUDBASE_ENV_ID=zemuresume-4gjvx1wea78e3d1e

# 系统配置
SYSTEM_PASSWORD=Aa123456
NODE_ENV=production

# 现有函数URL（用于参考和集成）
CVGENERATOR_URL=http://1341667342-jcyribeze9.ap-guangzhou.tencentscf.com
GATEWAY_URL=http://1341667342-digl3p9k4d.ap-guangzhou.tencentscf.com
JDWORKER_URL=http://1341667342-4p9m4skdlh.ap-guangzhou.tencentscf.com
RESUMEWORKER_URL=http://1341667342-21fo417pqr.ap-guangzhou.tencentscf.com

# CLS 日志服务
CLS_CONSOLE_URL=https://datasight-1341667342.clsconsole.tencent-cloud.com
CLS_ID=clsconsole-83d5b1b0

# 腾讯云CLI配置
TENCENT_CLI_KEY_NAME=cursor

# 函数配置
USER_LOGIN_FUNCTION_NAME=ai-resume-user-login
TOKEN_VERIFY_FUNCTION_NAME=ai-resume-token-verify
MAIN_API_FUNCTION_NAME=ai-resume-main-api

# 函数内存和超时配置
USER_LOGIN_MEMORY=128
USER_LOGIN_TIMEOUT=30
TOKEN_VERIFY_MEMORY=128
TOKEN_VERIFY_TIMEOUT=10
MAIN_API_MEMORY=256
MAIN_API_TIMEOUT=30
