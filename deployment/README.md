# AI Resume 认证系统部署指南

## 概述

本目录包含 AI Resume 认证系统的完整部署配置和脚本，用于一键部署三个核心 SCF 函数：

- **ai-resume-user-login** - 用户登录认证服务
- **ai-resume-token-verify** - Token验证中间件服务  
- **ai-resume-main-api** - 主API网关服务

## 文件结构

```
deployment/
├── README.md                    # 本文档
├── config.env                   # 主配置文件
├── deploy-all-functions.sh      # 一键部署脚本
└── verify-deployment.sh         # 部署验证脚本
```

## 前置要求

### 1. 安装腾讯云 CLI

```bash
# 安装腾讯云 CLI
pip install tccli

# 配置认证信息
tccli configure
```

配置时需要输入：
- SecretId: `YOUR_SECRET_ID`
- SecretKey: `YOUR_SECRET_KEY`
- Region: `ap-guangzhou`
- Output: `json`

### 2. 安装必要工具

```bash
# 确保已安装以下工具
which node    # Node.js (>=16.0.0)
which npm     # npm 包管理器
which zip     # 压缩工具
which jq      # JSON 处理工具
```

### 3. 获取微信小程序 Secret

1. 登录 [微信公众平台](https://mp.weixin.qq.com/)
2. 进入小程序管理后台
3. 在"开发" -> "开发管理" -> "开发设置"中获取 AppSecret
4. 更新 `config.env` 文件中的 `WECHAT_SECRET` 值

## 部署步骤

### 第一步：配置检查

```bash
# 检查配置文件
cat deployment/config.env

# 验证必要的配置项
grep -E "(TENCENT_SECRET_ID|WECHAT_APPID|JWT_SECRET)" deployment/config.env
```

### 第二步：执行部署

```bash
# 进入项目根目录
cd /path/to/ai-resume

# 执行一键部署
./deployment/deploy-all-functions.sh
```

部署脚本将自动：
1. 验证配置信息
2. 检查腾讯云 CLI
3. 为每个函数创建部署包
4. 创建或更新 SCF 函数
5. 配置 API 网关触发器
6. 设置环境变量和标签

### 第三步：验证部署

```bash
# 执行部署验证
./deployment/verify-deployment.sh
```

验证脚本将检查：
- 函数存在性和状态
- API 网关触发器配置
- 环境变量设置
- 函数可调用性

## 配置说明

### 主配置文件 (config.env)

| 配置项 | 说明 | 示例值 |
|--------|------|--------|
| TENCENT_SECRET_ID | 腾讯云 SecretId | YOUR_SECRET_ID |
| TENCENT_SECRET_KEY | 腾讯云 SecretKey | YOUR_SECRET_KEY |
| WECHAT_APPID | 微信小程序 AppID | wx2309e473610ea429 |
| WECHAT_SECRET | 微信小程序 Secret | 需要从微信公众平台获取 |
| JWT_SECRET | JWT 签名密钥 | ai-resume-jwt-secret-key-2024-wx2309e473610ea429 |

### 函数配置

| 函数 | 内存 | 超时 | 说明 |
|------|------|------|------|
| user-login | 128MB | 30s | 处理登录请求，需要调用微信API |
| token-verify | 128MB | 10s | 快速验证token，无外部依赖 |
| main-api | 256MB | 30s | 主要业务逻辑，需要调用其他函数 |

## 部署后配置

### 1. 获取函数 URL

部署完成后，在腾讯云控制台获取各函数的 API 网关 URL：

1. 访问 [SCF 控制台](https://console.cloud.tencent.com/scf/list?rid=1&ns=default)
2. 点击函数名称进入详情页
3. 在"触发管理"标签页查看 API 网关 URL

### 2. 更新主 API 函数环境变量

获取到登录和验证函数的 URL 后，需要更新主 API 函数的环境变量：

```bash
# 更新主 API 函数环境变量
tccli scf UpdateFunctionConfiguration \
  --region ap-guangzhou \
  --FunctionName ai-resume-main-api \
  --Environment '{
    "Variables": {
      "USER_LOGIN_FUNCTION_URL": "https://your-user-login-url",
      "TOKEN_VERIFY_FUNCTION_URL": "https://your-token-verify-url",
      "NODE_ENV": "production"
    }
  }'
```

### 3. 更新前端配置

更新前端代码中的 API 基础地址：

```javascript
// utils/http-api.js
static baseUrl = 'https://your-main-api-url'; // 替换为实际的主API函数URL
```

## 故障排除

### 常见问题

1. **部署失败：权限不足**
   ```
   解决方案：检查腾讯云 CLI 配置，确保 SecretId 和 SecretKey 正确
   ```

2. **函数创建失败：依赖安装错误**
   ```
   解决方案：检查网络连接，确保能够访问 npm 仓库
   ```

3. **API 网关触发器创建失败**
   ```
   解决方案：手动在控制台创建 API 网关触发器
   ```

4. **函数调用失败：环境变量缺失**
   ```
   解决方案：检查环境变量配置，特别是 WECHAT_SECRET
   ```

### 日志查看

```bash
# 查看函数执行日志
tccli scf GetFunctionLogs \
  --region ap-guangzhou \
  --FunctionName ai-resume-user-login \
  --StartTime "2024-01-15 00:00:00" \
  --EndTime "2024-01-15 23:59:59"
```

### 手动部署

如果自动部署失败，可以手动在控制台部署：

1. 访问 [SCF 控制台](https://console.cloud.tencent.com/scf)
2. 点击"新建函数"
3. 选择"自定义创建"
4. 上传对应的函数代码包
5. 配置环境变量和触发器

## 监控和维护

### 1. 设置监控告警

在腾讯云控制台设置以下监控指标：
- 函数调用次数
- 函数错误率
- 函数执行时间
- API 网关请求量

### 2. 定期检查

建议定期执行验证脚本：

```bash
# 每周执行一次验证
./deployment/verify-deployment.sh
```

### 3. 更新部署

当代码有更新时，重新执行部署脚本：

```bash
# 更新部署
./deployment/deploy-all-functions.sh
```

## 安全建议

1. **定期更换密钥**：定期更换 JWT_SECRET 和微信小程序 Secret
2. **访问控制**：为 SCF 函数设置适当的访问控制策略
3. **日志监控**：监控异常登录和 API 调用行为
4. **版本管理**：使用函数版本管理功能进行灰度发布

## 支持

如遇到问题，请：
1. 查看函数执行日志
2. 运行验证脚本诊断问题
3. 检查腾讯云控制台的函数状态
4. 参考腾讯云 SCF 官方文档
