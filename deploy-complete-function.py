#!/usr/bin/env python3
"""
部署包含完整依赖的ai-resume-user-login SCF函数
"""

import os
import json
import zipfile
import tempfile
import shutil
import subprocess
from tencentcloud.common import credential
from tencentcloud.common.profile.client_profile import ClientProfile
from tencentcloud.common.profile.http_profile import HttpProfile
from tencentcloud.scf.v20180416 import scf_client, models

# 腾讯云配置
SECRET_ID = "YOUR_SECRET_ID"
SECRET_KEY = "YOUR_SECRET_KEY"
REGION = "ap-guangzhou"
FUNCTION_NAME = "ai-resume-user-login"

def init_scf_client():
    """初始化SCF客户端"""
    cred = credential.Credential(SECRET_ID, SECRET_KEY)
    
    httpProfile = HttpProfile()
    httpProfile.endpoint = "scf.tencentcloudapi.com"
    
    clientProfile = ClientProfile()
    clientProfile.httpProfile = httpProfile
    
    return scf_client.ScfClient(cred, REGION, clientProfile)

def create_complete_deployment_package():
    """创建包含完整依赖的部署包"""
    print("📦 创建包含完整依赖的部署包...")
    
    # 创建临时目录
    temp_dir = tempfile.mkdtemp()
    function_dir = os.path.join(temp_dir, "function")
    os.makedirs(function_dir)
    
    try:
        # 复制函数文件
        source_dir = "auth-functions/user-login"
        
        # 复制index.js
        shutil.copy2(
            os.path.join(source_dir, "index.js"),
            os.path.join(function_dir, "index.js")
        )
        
        # 复制package.json
        shutil.copy2(
            os.path.join(source_dir, "package.json"),
            os.path.join(function_dir, "package.json")
        )
        
        # 安装npm依赖
        print("📥 安装npm依赖...")
        result = subprocess.run(
            ["npm", "install", "--production"],
            cwd=function_dir,
            capture_output=True,
            text=True
        )
        
        if result.returncode != 0:
            print(f"❌ npm install失败: {result.stderr}")
            return None
        
        print("✅ npm依赖安装成功")
        
        # 验证关键依赖是否存在
        node_modules_dir = os.path.join(function_dir, "node_modules")
        if not os.path.exists(os.path.join(node_modules_dir, "axios")):
            print("❌ axios依赖未正确安装")
            return None
        
        if not os.path.exists(os.path.join(node_modules_dir, "jsonwebtoken")):
            print("❌ jsonwebtoken依赖未正确安装")
            return None
        
        print("✅ 关键依赖验证通过")
        
        # 创建zip文件
        zip_path = os.path.join(temp_dir, "function-complete.zip")
        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for root, dirs, files in os.walk(function_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    arcname = os.path.relpath(file_path, function_dir)
                    zipf.write(file_path, arcname)
        
        print(f"✅ 完整部署包创建成功: {zip_path}")
        return zip_path
        
    except Exception as e:
        print(f"❌ 创建完整部署包失败: {e}")
        shutil.rmtree(temp_dir)
        return None

def deploy_complete_function(client, zip_path):
    """部署完整函数"""
    print("🚀 开始部署完整函数...")
    
    try:
        # 读取zip文件并进行base64编码
        import base64
        with open(zip_path, 'rb') as f:
            zip_content = f.read()
        
        # 更新函数代码
        req = models.UpdateFunctionCodeRequest()
        req.FunctionName = FUNCTION_NAME
        req.ZipFile = base64.b64encode(zip_content).decode('utf-8')
        
        resp = client.UpdateFunctionCode(req)
        print(f"✅ 完整函数代码更新成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 部署完整函数失败: {e}")
        return False

def comprehensive_test():
    """全面测试函数"""
    print("🧪 进行全面函数测试...")
    
    import requests
    import time
    
    # 等待函数部署完成
    print("⏳ 等待函数部署完成...")
    time.sleep(15)
    
    scf_url = "http://1341667342-l7dh36l4tt.ap-guangzhou.tencentscf.com"
    
    # 测试1: GET请求
    print(f"\n📡 测试1 - GET请求: {scf_url}")
    try:
        response = requests.get(scf_url, timeout=10)
        print(f"   状态码: {response.status_code}")
        print(f"   响应内容: {response.text}")
        
        if response.status_code == 200:
            print("✅ GET请求测试成功")
            get_success = True
        else:
            print(f"❌ GET请求失败，状态码: {response.status_code}")
            get_success = False
            
    except Exception as e:
        print(f"❌ GET请求测试失败: {e}")
        get_success = False
    
    # 测试2: POST请求（使用无效code测试错误处理）
    print(f"\n📡 测试2 - POST请求错误处理: {scf_url}")
    test_data = {
        "code": "invalid_test_code_12345",
        "loginType": "wechat"
    }
    
    try:
        response = requests.post(
            scf_url, 
            json=test_data,
            headers={'Content-Type': 'application/json'},
            timeout=15
        )
        print(f"   状态码: {response.status_code}")
        print(f"   响应内容: {response.text}")
        
        if response.status_code == 400:
            print("✅ POST请求错误处理测试成功")
            post_success = True
        else:
            print(f"⚠️ POST请求状态码异常: {response.status_code}")
            post_success = False
            
    except Exception as e:
        print(f"❌ POST请求测试失败: {e}")
        post_success = False
    
    # 测试3: OPTIONS请求
    print(f"\n📡 测试3 - OPTIONS请求: {scf_url}")
    try:
        response = requests.options(scf_url, timeout=10)
        print(f"   状态码: {response.status_code}")
        print(f"   响应内容: {response.text}")
        
        if response.status_code == 200:
            print("✅ OPTIONS请求测试成功")
            options_success = True
        else:
            print(f"❌ OPTIONS请求失败，状态码: {response.status_code}")
            options_success = False
            
    except Exception as e:
        print(f"❌ OPTIONS请求测试失败: {e}")
        options_success = False
    
    # 生成测试报告
    print(f"\n📊 测试结果汇总:")
    print(f"   GET请求: {'✅ 通过' if get_success else '❌ 失败'}")
    print(f"   POST请求: {'✅ 通过' if post_success else '❌ 失败'}")
    print(f"   OPTIONS请求: {'✅ 通过' if options_success else '❌ 失败'}")
    
    overall_success = get_success and post_success and options_success
    
    if overall_success:
        print("\n🎉 所有测试通过！SCF函数500错误已完全修复！")
        print("✅ 函数现在可以正常处理所有类型的HTTP请求")
    else:
        print("\n⚠️ 部分测试失败，需要进一步调试")
    
    return overall_success

def main():
    """主函数"""
    print("🚀 开始部署包含完整依赖的SCF函数...")
    print("=" * 60)
    
    # 初始化客户端
    client = init_scf_client()
    
    # 创建完整部署包
    zip_path = create_complete_deployment_package()
    if not zip_path:
        return False
    
    try:
        # 部署函数
        if deploy_complete_function(client, zip_path):
            print("\n" + "=" * 60)
            # 全面测试函数
            success = comprehensive_test()
            
            if success:
                print("\n🎉 SCF函数修复和部署完全成功！")
                print("✅ 500错误已修复")
                print("✅ 依赖问题已解决")
                print("✅ 所有HTTP方法都能正常工作")
            else:
                print("\n⚠️ 函数部署成功但测试未完全通过")
            
            return success
        else:
            return False
            
    finally:
        # 清理临时文件
        if zip_path and os.path.exists(zip_path):
            os.remove(zip_path)
            print("🧹 清理临时文件完成")

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
