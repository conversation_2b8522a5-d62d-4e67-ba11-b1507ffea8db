#!/usr/bin/env python3
"""
修复SCF函数配置脚本 - 解决401认证错误
统一使用zemu简历配置：wx4fa04593aaf3bb76
"""

import json
from tencentcloud.common import credential
from tencentcloud.common.profile.client_profile import ClientProfile
from tencentcloud.common.profile.http_profile import HttpProfile
from tencentcloud.scf.v20180416 import scf_client, models

# 腾讯云认证信息
SECRET_ID = "YOUR_SECRET_ID"
SECRET_KEY = "YOUR_SECRET_KEY"
REGION = "ap-guangzhou"

def init_scf_client():
    """初始化SCF客户端"""
    cred = credential.Credential(SECRET_ID, SECRET_KEY)
    
    httpProfile = HttpProfile()
    httpProfile.endpoint = "scf.tencentcloudapi.com"
    
    clientProfile = ClientProfile()
    clientProfile.httpProfile = httpProfile
    
    return scf_client.ScfClient(cred, REGION, clientProfile)

def update_function_config(client, function_name, environment_vars):
    """更新函数环境变量"""
    try:
        req = models.UpdateFunctionConfigurationRequest()
        params = {
            "FunctionName": function_name,
            "Environment": {
                "Variables": environment_vars
            }
        }
        req.from_json_string(json.dumps(params))
        
        resp = client.UpdateFunctionConfiguration(req)
        print(f"✅ {function_name} 函数配置更新成功")
        return True
        
    except Exception as e:
        print(f"❌ {function_name} 函数配置更新失败: {str(e)}")
        return False

def main():
    print("🔧 开始修复SCF函数配置...")
    
    # 初始化客户端
    client = init_scf_client()
    
    # 1. 更新ai-resume-user-login函数 - 使用zemu简历配置
    print("📝 更新ai-resume-user-login函数配置...")
    login_env = {
        "WECHAT_APPID": "wx4fa04593aaf3bb76",
        "WECHAT_SECRET": "4a90dd7e501e24f0489e2b83031e1100",
        "JWT_SECRET": "ai-resume-jwt-secret-key-2024-wx4fa04593aaf3bb76",
        "JWT_EXPIRES_IN": "7d",
        "NODE_ENV": "production"
    }
    update_function_config(client, "ai-resume-user-login", login_env)
    
    # 2. 更新ai-resume-token-verify函数 - 添加JWT_SECRET
    print("🔐 更新ai-resume-token-verify函数配置...")
    verify_env = {
        "JWT_SECRET": "ai-resume-jwt-secret-key-2024-wx4fa04593aaf3bb76",
        "NODE_ENV": "production"
    }
    update_function_config(client, "ai-resume-token-verify", verify_env)
    
    # 3. 更新ai-resume-main-api函数 - 添加函数URL配置
    print("🌐 更新ai-resume-main-api函数配置...")
    api_env = {
        "USER_LOGIN_FUNCTION_URL": "http://1341667342-l7dh36l4tt.ap-guangzhou.tencentscf.com",
        "TOKEN_VERIFY_FUNCTION_URL": "http://1341667342-ldcw844wvx.ap-guangzhou.tencentscf.com",
        "NODE_ENV": "production",
        "WECHAT_APPID": "wx4fa04593aaf3bb76"
    }
    update_function_config(client, "ai-resume-main-api", api_env)
    
    print("")
    print("🎉 SCF函数配置修复完成！")
    print("📋 修复内容：")
    print("   1. 统一使用zemu简历配置 (wx4fa04593aaf3bb76)")
    print("   2. 修复JWT密钥一致性问题")
    print("   3. 添加缺失的函数URL环境变量")
    print("")
    print("⏳ 请等待1-2分钟让配置生效，然后重新测试登录功能")

if __name__ == "__main__":
    main()
