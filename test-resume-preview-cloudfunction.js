/**
 * 测试简历预览云函数
 * 验证纯云函数版本的简历预览生成功能
 */

const cloud = require('wx-server-sdk');

// 初始化云开发环境
cloud.init({
    env: 'zemuresume-4gjvx1wea78e3d1e'
});

// 测试简历数据
const testResumeData = {
    personalInfo: {
        name: '张三',
        email: 'zhang<PERSON>@example.com',
        phone: '13800138000'
    },
    summary: '具有5年前端开发经验的软件工程师，熟练掌握React、Vue等现代前端框架，有丰富的项目开发和团队协作经验。',
    workExperience: [
        {
            position: '高级前端工程师',
            company: '腾讯科技有限公司',
            startDate: '2021-03',
            endDate: '至今',
            description: '负责微信小程序和Web应用的前端开发，参与多个千万级用户产品的开发和维护。'
        },
        {
            position: '前端工程师',
            company: '阿里巴巴集团',
            startDate: '2019-06',
            endDate: '2021-02',
            description: '参与淘宝商家后台系统的开发，使用React和TypeScript构建高性能的管理界面。'
        }
    ],
    education: [
        {
            degree: '本科',
            major: '计算机科学与技术',
            school: '清华大学',
            startDate: '2015-09',
            endDate: '2019-06'
        }
    ],
    skills: ['JavaScript', 'React', 'Vue.js', 'TypeScript', 'Node.js', 'Python', 'MySQL', 'MongoDB']
};

async function testResumePreview() {
    console.log('🚀 开始测试简历预览云函数...');
    
    try {
        // 测试PNG生成
        console.log('📸 测试PNG预览生成...');
        const pngResult = await cloud.callFunction({
            name: 'resumePreviewGenerator',
            data: {
                resumeData: testResumeData,
                format: 'png'
            }
        });
        
        console.log('✅ PNG预览测试结果:', JSON.stringify(pngResult.result, null, 2));
        
        // 测试HTML内容生成
        console.log('📄 测试HTML内容生成...');
        const htmlResult = await cloud.callFunction({
            name: 'resumePreviewGenerator',
            data: {
                resumeData: testResumeData,
                format: 'html'
            }
        });
        
        console.log('✅ HTML内容测试结果:', JSON.stringify(htmlResult.result, null, 2));
        
        console.log('🎉 所有测试完成！');
        
    } catch (error) {
        console.error('❌ 测试失败:', error);
        console.error('错误详情:', error.message);
        if (error.errMsg) {
            console.error('云函数错误信息:', error.errMsg);
        }
    }
}

// 执行测试
testResumePreview().then(() => {
    console.log('测试脚本执行完成');
}).catch(error => {
    console.error('测试脚本执行失败:', error);
});
