/**
 * 测试云托管服务连接性
 * 通过云函数验证云托管服务是否真的在运行
 */

const http = require('http');
const https = require('https');

// 测试不同的URL格式
const testUrls = [
    // 内网地址格式
    'http://resume-snapshot/health',
    'http://resume-snapshot.internal/health',
    'http://resume-snapshot.zemuresume-4gjvx1wea78e3d1e.internal/health',
    'http://resume-snapshot-zemuresume-4gjvx1wea78e3d1e.internal/health',
    
    // 外网地址格式
    'https://resume-snapshot-zemuresume-4gjvx1wea78e3d1e.ap-shanghai.run.tcloudbase.com/health',
    'https://zemuresume-4gjvx1wea78e3d1e-resume-snapshot.ap-shanghai.run.tcloudbase.com/health',
    'https://resume-snapshot-zemuresume-4gjvx1wea78e3d1e.service.tcloudbase.com/health',
    'https://zemuresume-4gjvx1wea78e3d1e-resume-snapshot.service.tcloudbase.com/health'
];

async function testConnection(url) {
    return new Promise((resolve) => {
        console.log(`🔍 测试连接: ${url}`);
        
        const isHttps = url.startsWith('https');
        const client = isHttps ? https : http;
        const urlObj = new URL(url);
        
        const options = {
            hostname: urlObj.hostname,
            port: urlObj.port || (isHttps ? 443 : 80),
            path: urlObj.pathname,
            method: 'GET',
            timeout: 10000,
            headers: {
                'User-Agent': 'CloudBase-Function-Test'
            }
        };

        const req = client.request(options, (res) => {
            let data = '';
            res.on('data', (chunk) => {
                data += chunk;
            });
            
            res.on('end', () => {
                console.log(`   状态码: ${res.statusCode}`);
                console.log(`   响应: ${data.substring(0, 200)}${data.length > 200 ? '...' : ''}`);
                
                resolve({
                    url,
                    success: res.statusCode === 200,
                    statusCode: res.statusCode,
                    response: data,
                    error: null
                });
            });
        });

        req.on('error', (error) => {
            console.log(`   错误: ${error.message}`);
            resolve({
                url,
                success: false,
                statusCode: null,
                response: null,
                error: error.message
            });
        });

        req.on('timeout', () => {
            console.log(`   超时`);
            req.destroy();
            resolve({
                url,
                success: false,
                statusCode: null,
                response: null,
                error: 'timeout'
            });
        });

        req.end();
    });
}

async function runConnectivityTest() {
    console.log('🚀 开始云托管服务连接性测试\n');
    
    const results = [];
    
    for (const url of testUrls) {
        const result = await testConnection(url);
        results.push(result);
        console.log(''); // 空行分隔
        
        // 如果找到成功的连接，记录并继续测试其他格式
        if (result.success) {
            console.log(`✅ 找到可用的URL: ${result.url}`);
        }
    }
    
    // 总结结果
    console.log('📊 测试结果总结:');
    const successfulUrls = results.filter(r => r.success);
    const failedUrls = results.filter(r => !r.success);
    
    console.log(`✅ 成功连接: ${successfulUrls.length}/${results.length}`);
    console.log(`❌ 连接失败: ${failedUrls.length}/${results.length}`);
    
    if (successfulUrls.length > 0) {
        console.log('\n🎉 可用的URL:');
        successfulUrls.forEach(result => {
            console.log(`   ${result.url}`);
        });
    } else {
        console.log('\n❌ 没有找到可用的URL');
        console.log('\n🔍 常见错误类型:');
        const errorTypes = {};
        failedUrls.forEach(result => {
            const errorType = result.error || `HTTP ${result.statusCode}`;
            errorTypes[errorType] = (errorTypes[errorType] || 0) + 1;
        });
        
        Object.entries(errorTypes).forEach(([error, count]) => {
            console.log(`   ${error}: ${count} 次`);
        });
    }
    
    return {
        total: results.length,
        successful: successfulUrls.length,
        failed: failedUrls.length,
        results: results
    };
}

// 如果直接运行此脚本
if (require.main === module) {
    runConnectivityTest().then(summary => {
        console.log('\n📋 最终结论:');
        if (summary.successful > 0) {
            console.log('✅ 云托管服务正在运行并且可以访问');
        } else {
            console.log('❌ 云托管服务无法访问，可能的原因:');
            console.log('   1. 服务虽然显示normal但实际未启动');
            console.log('   2. 网络配置或防火墙问题');
            console.log('   3. URL格式不正确');
            console.log('   4. 需要特殊的认证或权限');
        }
    }).catch(console.error);
}

module.exports = { runConnectivityTest, testConnection };
