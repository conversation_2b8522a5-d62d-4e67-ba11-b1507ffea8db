#!/usr/bin/env python3
"""
调试SCF函数日志脚本
使用腾讯云SDK查询ai-resume-user-login函数的CLS日志
"""

import json
import time
from datetime import datetime, timedelta
from tencentcloud.common import credential
from tencentcloud.common.profile.client_profile import ClientProfile
from tencentcloud.common.profile.http_profile import HttpProfile
from tencentcloud.cls.v20201016 import cls_client, models as cls_models
from tencentcloud.scf.v20180416 import scf_client, models as scf_models

# 腾讯云配置
SECRET_ID = "YOUR_SECRET_ID"
SECRET_KEY = "YOUR_SECRET_KEY"
REGION = "ap-guangzhou"
FUNCTION_NAME = "ai-resume-user-login"

def init_clients():
    """初始化腾讯云客户端"""
    cred = credential.Credential(SECRET_ID, SECRET_KEY)
    
    # HTTP配置
    httpProfile = HttpProfile()
    httpProfile.endpoint = "cls.tencentcloudapi.com"
    
    # 客户端配置
    clientProfile = ClientProfile()
    clientProfile.httpProfile = httpProfile
    
    # 初始化CLS客户端
    cls_client_instance = cls_client.ClsClient(cred, REGION, clientProfile)
    
    # 初始化SCF客户端
    scf_httpProfile = HttpProfile()
    scf_httpProfile.endpoint = "scf.tencentcloudapi.com"
    scf_clientProfile = ClientProfile()
    scf_clientProfile.httpProfile = scf_httpProfile
    scf_client_instance = scf_client.ScfClient(cred, REGION, scf_clientProfile)
    
    return cls_client_instance, scf_client_instance

def get_function_info(scf_client_instance):
    """获取SCF函数信息"""
    try:
        req = scf_models.GetFunctionRequest()
        req.FunctionName = FUNCTION_NAME
        
        resp = scf_client_instance.GetFunction(req)
        print(f"✅ 函数信息获取成功:")
        print(f"   函数名: {resp.Configuration.FunctionName}")
        print(f"   运行时: {resp.Configuration.Runtime}")
        print(f"   状态: {resp.Configuration.Status}")
        print(f"   最后修改时间: {resp.Configuration.ModTime}")
        
        return resp.Configuration
        
    except Exception as e:
        print(f"❌ 获取函数信息失败: {e}")
        return None

def get_function_logs(cls_client_instance):
    """获取函数日志"""
    try:
        # 查询最近1小时的日志
        end_time = datetime.now()
        start_time = end_time - timedelta(hours=1)
        
        # 转换为毫秒时间戳
        start_timestamp = int(start_time.timestamp() * 1000)
        end_timestamp = int(end_time.timestamp() * 1000)
        
        print(f"🔍 查询时间范围: {start_time.strftime('%Y-%m-%d %H:%M:%S')} - {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 构建查询请求
        req = cls_models.SearchLogRequest()
        req.TopicId = "your-topic-id"  # 需要从CLS控制台获取
        req.From = start_timestamp
        req.To = end_timestamp
        req.Query = f"FunctionName:{FUNCTION_NAME}"
        req.Limit = 100
        
        # 由于我们没有具体的TopicId，我们使用另一种方法
        # 直接查询SCF函数的调用日志
        return get_scf_function_logs_alternative()
        
    except Exception as e:
        print(f"❌ 查询CLS日志失败: {e}")
        return None

def get_scf_function_logs_alternative():
    """使用SCF API获取函数调用日志"""
    try:
        # 这里我们创建一个测试请求来触发函数并观察错误
        print("🧪 创建测试请求来触发函数...")
        
        import requests
        
        # 测试SCF函数URL
        scf_url = "http://1341667342-l7dh36l4tt.ap-guangzhou.tencentscf.com"
        
        # 发送GET请求测试连通性
        print(f"📡 测试GET请求: {scf_url}")
        try:
            response = requests.get(scf_url, timeout=10)
            print(f"   状态码: {response.status_code}")
            print(f"   响应头: {dict(response.headers)}")
            print(f"   响应内容: {response.text[:500]}...")
        except Exception as e:
            print(f"   GET请求失败: {e}")
        
        # 发送POST请求测试登录功能
        print(f"\n📡 测试POST请求: {scf_url}")
        test_data = {
            "code": "test_debug_code_12345",
            "loginType": "wechat",
            "userInfo": {
                "nickName": "测试用户"
            }
        }
        
        try:
            response = requests.post(
                scf_url, 
                json=test_data,
                headers={'Content-Type': 'application/json'},
                timeout=15
            )
            print(f"   状态码: {response.status_code}")
            print(f"   响应头: {dict(response.headers)}")
            print(f"   响应内容: {response.text}")
            
            if response.status_code == 500:
                print("\n🚨 发现500错误！")
                print("   这确认了问题的存在")
                
        except Exception as e:
            print(f"   POST请求失败: {e}")
            
        return True
        
    except Exception as e:
        print(f"❌ 测试请求失败: {e}")
        return False

def analyze_error_patterns():
    """分析可能的错误模式"""
    print("\n🔍 分析可能的错误原因:")
    print("1. 环境变量配置问题:")
    print("   - WECHAT_APPID 未设置或错误")
    print("   - WECHAT_SECRET 未设置或错误")
    print("   - JWT_SECRET 未设置")
    
    print("\n2. 依赖项问题:")
    print("   - axios 模块未正确安装")
    print("   - jsonwebtoken 模块未正确安装")
    print("   - 其他npm依赖缺失")
    
    print("\n3. 代码逻辑问题:")
    print("   - 未处理的异常")
    print("   - 数据库连接失败")
    print("   - 微信API调用失败")
    
    print("\n4. 权限问题:")
    print("   - SCF函数执行角色权限不足")
    print("   - 网络访问权限问题")

def main():
    """主函数"""
    print("🚀 开始调试SCF函数日志...")
    print("=" * 60)
    
    # 初始化客户端
    cls_client_instance, scf_client_instance = init_clients()
    
    # 获取函数信息
    function_info = get_function_info(scf_client_instance)
    
    # 获取函数日志
    print("\n" + "=" * 60)
    get_function_logs(cls_client_instance)
    
    # 分析错误模式
    print("\n" + "=" * 60)
    analyze_error_patterns()
    
    print("\n✅ 日志调试完成")

if __name__ == "__main__":
    main()
