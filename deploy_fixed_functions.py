#!/usr/bin/env python3
"""
部署修复后的SCF函数 - 解决401认证错误
统一使用zemu简历配置：wx4fa04593aaf3bb76
"""

import os
import zipfile
import json
from tencentcloud.common import credential
from tencentcloud.common.profile.client_profile import ClientProfile
from tencentcloud.common.profile.http_profile import HttpProfile
from tencentcloud.scf.v20180416 import scf_client, models

# 腾讯云认证信息
SECRET_ID = "YOUR_SECRET_ID"
SECRET_KEY = "YOUR_SECRET_KEY"
REGION = "ap-guangzhou"

def init_scf_client():
    """初始化SCF客户端"""
    cred = credential.Credential(SECRET_ID, SECRET_KEY)
    
    httpProfile = HttpProfile()
    httpProfile.endpoint = "scf.tencentcloudapi.com"
    
    clientProfile = ClientProfile()
    clientProfile.httpProfile = httpProfile
    
    return scf_client.ScfClient(cred, REGION, clientProfile)

def create_function_zip(source_dir, zip_path):
    """创建函数代码压缩包"""
    print(f"📦 创建压缩包: {zip_path}")
    
    with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk(source_dir):
            for file in files:
                if file.endswith(('.js', '.json')):
                    file_path = os.path.join(root, file)
                    arcname = os.path.relpath(file_path, source_dir)
                    zipf.write(file_path, arcname)
                    print(f"  添加文件: {arcname}")
    
    print(f"✅ 压缩包创建完成: {zip_path}")

def update_function_code(client, function_name, zip_path):
    """更新函数代码"""
    try:
        print(f"🚀 更新函数代码: {function_name}")
        
        # 读取压缩包
        with open(zip_path, 'rb') as f:
            zip_content = f.read()
        
        req = models.UpdateFunctionCodeRequest()
        params = {
            "FunctionName": function_name,
            "ZipFile": zip_content
        }
        req.from_json_string(json.dumps(params))
        
        resp = client.UpdateFunctionCode(req)
        print(f"✅ {function_name} 代码更新成功")
        return True
        
    except Exception as e:
        print(f"❌ {function_name} 代码更新失败: {str(e)}")
        return False

def main():
    print("🔧 开始部署修复后的SCF函数...")
    
    # 初始化客户端
    client = init_scf_client()
    
    # 1. 部署token验证函数
    print("\n📝 部署ai-resume-token-verify函数...")
    token_verify_zip = "token-verify-fixed.zip"
    create_function_zip("auth-functions/token-verify", token_verify_zip)
    update_function_code(client, "ai-resume-token-verify", token_verify_zip)
    
    # 2. 部署用户登录函数
    print("\n🔐 部署ai-resume-user-login函数...")
    user_login_zip = "user-login-fixed.zip"
    create_function_zip("auth-functions/user-login", user_login_zip)
    update_function_code(client, "ai-resume-user-login", user_login_zip)
    
    # 3. 部署主API函数
    print("\n🌐 部署ai-resume-main-api函数...")
    main_api_zip = "main-api-fixed.zip"
    create_function_zip("scf-function", main_api_zip)
    update_function_code(client, "ai-resume-main-api", main_api_zip)
    
    # 清理临时文件
    print("\n🧹 清理临时文件...")
    for zip_file in [token_verify_zip, user_login_zip, main_api_zip]:
        if os.path.exists(zip_file):
            os.remove(zip_file)
            print(f"  删除: {zip_file}")
    
    print("")
    print("🎉 SCF函数部署完成！")
    print("📋 修复内容：")
    print("   1. 统一使用zemu简历配置 (wx4fa04593aaf3bb76)")
    print("   2. 修复JWT密钥一致性问题")
    print("   3. 更新函数代码使用正确配置")
    print("")
    print("⏳ 请等待1-2分钟让部署生效，然后重新测试登录功能")

if __name__ == "__main__":
    main()
