/**
 * cv-generator.test.js
 * 测试部署的cv-generator云函数
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');
const tencentcloud = require('tencentcloud-sdk-nodejs');
const ScfClient = tencentcloud.scf.v20180416.Client;

// 配置
const REGION = 'ap-guangzhou'; // 修正为正确的区域
const SECRET_ID = 'YOUR_SECRET_ID'; // For debugging only
const SECRET_KEY = 'YOUR_SECRET_KEY'; // For debugging only
const FUNCTION_NAME = 'cv-generator';
const OUTPUT_DIR = path.join(__dirname, '../test-results');

// 确保输出目录存在
if (!fs.existsSync(OUTPUT_DIR)) {
  fs.mkdirSync(OUTPUT_DIR, { recursive: true });
}

// 创建SCF客户端
const clientConfig = {
  credential: {
    secretId: SECRET_ID,
    secretKey: SECRET_KEY,
  },
  region: REGION,
  profile: {
    httpProfile: {
      endpoint: "scf.tencentcloudapi.com",
    },
  },
};
const client = new ScfClient(clientConfig);

// 测试数据
const testData = {
  userId: "test-user-" + Date.now(),
  resumeData: {
    personalInfo: {
      name: "张三",
      email: "<EMAIL>",
      phone: "13800138000",
      title: "全栈开发工程师",
      summary: "5年全栈开发经验，精通前端和后端技术，有丰富的项目经验。"
    },
    education: [
      {
        school: "北京大学",
        degree: "计算机科学硕士",
        startDate: "2015-09",
        endDate: "2018-07",
        description: "主修人工智能和机器学习"
      },
      {
        school: "清华大学",
        degree: "计算机科学学士",
        startDate: "2011-09",
        endDate: "2015-07",
        description: "主修软件工程"
      }
    ],
    workExperience: [
      {
        company: "腾讯科技",
        position: "高级开发工程师",
        startDate: "2018-08",
        endDate: "至今",
        description: "负责微信支付核心系统的开发和维护，优化系统性能，提高交易成功率。"
      },
      {
        company: "阿里巴巴",
        position: "开发工程师",
        startDate: "2015-07",
        endDate: "2018-07",
        description: "参与电商平台后端API开发，实现订单管理和支付系统。"
      }
    ],
    skills: [
      { name: "JavaScript", level: "专家" },
      { name: "React", level: "专家" },
      { name: "Node.js", level: "高级" },
      { name: "Python", level: "中级" },
      { name: "Docker", level: "中级" },
      { name: "微服务架构", level: "高级" }
    ],
    projects: [
      {
        name: "企业级支付系统",
        description: "设计并实现高并发支付处理系统，日交易额超过1亿元，系统可用性达99.99%。",
        technologies: ["Node.js", "Redis", "MySQL", "Docker"]
      },
      {
        name: "电商平台重构",
        description: "将单体应用重构为微服务架构，提高系统扩展性和可维护性，减少50%的部署时间。",
        technologies: ["React", "Spring Boot", "Kubernetes", "Kafka"]
      }
    ]
  },
  jdData: {
    jobTitle: "高级全栈开发工程师",
    company: "字节跳动",
    requiredSkills: ["JavaScript", "React", "Node.js", "微服务", "云原生"],
    preferredSkills: ["TypeScript", "Kubernetes", "GraphQL"],
    jobDescription: "负责公司核心产品的前后端开发，优化系统性能，提升用户体验。要求有扎实的编程功底和丰富的项目经验。"
  },
  template: "modern" // 简历模板: modern, classic, professional
};

/**
 * 直接调用云函数
 */
async function invokeFunction() {
  try {
    console.log(`\n🚀 开始测试云函数: ${FUNCTION_NAME}`);
    
    // 将整个测试数据作为payload
    const payload = {
      body: JSON.stringify(testData)
    };
    
    const params = {
      FunctionName: FUNCTION_NAME,
      InvocationType: "RequestResponse", // 同步调用
      LogType: 'Tail', // 返回最后4KB的日志
      ClientContext: JSON.stringify(payload), // 传递给函数的上下文信息
      Namespace: "default"
    };
    
    console.log('  调用参数 (部分):', { ...params, ClientContext: '...' });
    const result = await client.Invoke(params);
    
    // 保存原始响应
    const timestamp = Date.now();
    const resultPath = path.join(OUTPUT_DIR, `cv-generator-invoke-result-${timestamp}.json`);
    fs.writeFileSync(resultPath, JSON.stringify(result, null, 2));
    console.log(`  ✅ 原始响应已保存到: ${resultPath}`);
    
    // 解析并处理函数返回
    if (result.Result && result.Result.RetMsg) {
      let functionResponse;
      try {
        // RetMsg是函数返回的JSON字符串，直接解析
        functionResponse = JSON.parse(result.Result.RetMsg);
        console.log('  ↩️ 函数返回值:', functionResponse);
        
        // 进一步解析body
        if (typeof functionResponse.body === 'string') {
          const body = JSON.parse(functionResponse.body);
          console.log('  📄 函数返回Body:', body);

          if (body.success && body.pdfUrl) {
            console.log('  🎉 PDF生成成功!');
            console.log('     PDF URL:', body.pdfUrl);
            await downloadPdf(body.pdfUrl, timestamp);
          } else {
            console.error('  ❌ PDF生成失败:', body.error || '未知错误');
          }
        }

      } catch (e) {
        console.error('  ❌ 解析函数返回值失败:', e);
        console.log('  原始RetMsg:', result.Result.RetMsg);
      }
    } else {
      console.error('  ❌ 函数未返回有效结果:', result);
    }

    // 打印函数执行日志
    if (result.Log) {
      console.log('\n📋 函数执行日志 (最后4KB):');
      console.log('-----------------------------------');
      console.log(Buffer.from(result.Log, 'base64').toString());
      console.log('-----------------------------------');
    }

  } catch (error) {
    console.error('❌ 调用云函数时发生严重错误:', error);
    throw error;
  }
}

/**
 * 下载生成的PDF文件
 */
async function downloadPdf(pdfUrl, timestamp) {
  try {
    console.log(`开始下载PDF: ${pdfUrl}`);
    
    const response = await axios({
      method: 'GET',
      url: pdfUrl,
      responseType: 'arraybuffer'
    });
    
    const pdfPath = path.join(OUTPUT_DIR, `cv-${timestamp}.pdf`);
    fs.writeFileSync(pdfPath, response.data);
    
    console.log(`✅ PDF已下载到: ${pdfPath}`);
    
    // 验证PDF文件大小
    const stats = fs.statSync(pdfPath);
    console.log(`PDF文件大小: ${(stats.size / 1024).toFixed(2)} KB`);
    
    if (stats.size < 1024) { // 小于1KB可能有问题
      console.warn('⚠️ 警告: PDF文件大小异常小，可能生成有问题');
    }
    
    return pdfPath;
  } catch (error) {
    console.error('❌ 下载PDF失败:', error);
    throw error;
  }
}

/**
 * 检查云函数执行日志
 */
async function checkFunctionLogs() {
  try {
    console.log(`获取云函数日志: ${FUNCTION_NAME}`);
    
    // 计算过去10分钟的时间范围
    const endTime = Math.floor(Date.now() / 1000);
    const startTime = endTime - 10 * 60; // 10分钟前
    
    const params = {
      FunctionName: FUNCTION_NAME,
      Namespace: "default",
      StartTime: startTime,
      EndTime: endTime,
      Limit: 10
    };
    
    const result = await client.GetFunctionLogs(params);
    
    if (result && result.Data && result.Data.length > 0) {
      console.log('最近的函数日志:');
      result.Data.forEach((log, index) => {
        console.log(`--- 日志 ${index + 1} ---`);
        console.log(`时间: ${log.StartTime}`);
        console.log(`请求ID: ${log.RequestId}`);
        console.log(`内容: ${log.Log}`);
        console.log('-------------------');
      });
    } else {
      console.log('没有找到最近的函数日志');
    }
    
    return result;
  } catch (error) {
    console.error('❌ 获取函数日志失败:', error);
    return null;
  }
}

/**
 * 主测试函数
 */
async function runTest() {
  console.log('=== 开始测试cv-generator云函数 ===');
  console.log(`时间: ${new Date().toISOString()}`);
  
  try {
    await invokeFunction();
    console.log('=== 测试完成 ===');
  } catch (error) {
    console.error('=== 测试失败 ===', error);
    process.exit(1);
  }
}

// 执行测试
if (require.main === module) {
  if (!SECRET_ID || !SECRET_KEY) {
    console.error('❌ 错误: 环境变量TENCENTCLOUD_SECRET_ID和TENCENTCLOUD_SECRET_KEY必须设置');
    process.exit(1);
  }
  
  runTest();
}

module.exports = {
  runTest,
  invokeFunction,
  downloadPdf,
  checkFunctionLogs
}; 