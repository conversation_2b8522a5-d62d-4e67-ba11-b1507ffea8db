const tencentcloud = require("tencentcloud-sdk-nodejs");

// 腾讯云SCF客户端配置
const ScfClient = tencentcloud.scf.v20180416.Client;

const clientConfig = {
    credential: {
        secretId: process.env.TENCENT_SECRET_ID || 'YOUR_SECRET_ID',
        secretKey: process.env.TENCENT_SECRET_KEY || 'YOUR_SECRET_KEY',
    },
    region: "ap-guangzhou",
    profile: {
        httpProfile: {
            endpoint: "scf.tencentcloudapi.com",
        },
    },
};

/**
 * 为SCF函数添加HTTP触发器
 */
async function addHttpTrigger(functionName) {
    const client = new ScfClient(clientConfig);
    
    try {
        const params = {
            FunctionName: functionName,
            TriggerName: `${functionName}-http`,
            Type: "apigw",
            TriggerDesc: JSON.stringify({
                api: {
                    authRequired: "FALSE",
                    requestConfig: {
                        method: "ANY"  // 支持所有HTTP方法
                    },
                    isIntegratedResponse: "FALSE"
                },
                service: {
                    serviceName: "SCF_API_SERVICE"
                },
                release: {
                    environmentName: "release"
                }
            })
        };
        
        const result = await client.CreateTrigger(params);
        console.log(`✅ ${functionName} HTTP触发器创建成功`);
        
        // 获取触发器信息以显示访问URL
        const getTriggerParams = {
            FunctionName: functionName
        };
        
        const triggerInfo = await client.GetFunction(getTriggerParams);
        const triggers = triggerInfo.Triggers || [];
        const httpTrigger = triggers.find(t => t.Type === 'apigw');
        
        if (httpTrigger && httpTrigger.TriggerDesc) {
            try {
                const triggerDesc = JSON.parse(httpTrigger.TriggerDesc);
                console.log(`   📡 访问URL: ${triggerDesc.service.serviceUrl || '稍后在控制台查看'}`);
            } catch (e) {
                console.log(`   📡 访问URL: 请在控制台查看详细信息`);
            }
        }
        
        return result;
    } catch (error) {
        if (error.code === 'ResourceInUse.Trigger') {
            console.log(`📋 ${functionName} HTTP触发器已存在`);
            
            // 获取现有触发器信息
            try {
                const getTriggerParams = {
                    FunctionName: functionName
                };
                
                const triggerInfo = await client.GetFunction(getTriggerParams);
                const triggers = triggerInfo.Triggers || [];
                const httpTrigger = triggers.find(t => t.Type === 'apigw');
                
                if (httpTrigger && httpTrigger.TriggerDesc) {
                    try {
                        const triggerDesc = JSON.parse(httpTrigger.TriggerDesc);
                        console.log(`   📡 访问URL: ${triggerDesc.service.serviceUrl || '请在控制台查看'}`);
                    } catch (e) {
                        console.log(`   📡 访问URL: 请在控制台查看详细信息`);
                    }
                }
            } catch (getError) {
                console.log(`   📡 请在控制台查看访问URL`);
            }
            
            return true;
        }
        console.error(`❌ 创建${functionName} HTTP触发器失败:`, error.message);
        console.error(`   错误代码: ${error.code}`);
        return null;
    }
}

/**
 * 主配置流程
 */
async function setupHttpTriggers() {
    console.log('🚀 开始为SCF函数配置HTTP触发器...');
    console.log('========================================');
    
    const functions = [
        'resume-gateway',
        'task-dispatcher', 
        'slice-worker',
        'get-task-progress',
        'result-aggregator'
    ];
    
    let successCount = 0;
    
    for (const functionName of functions) {
        console.log(`\n🔧 配置函数: ${functionName}`);
        
        const result = await addHttpTrigger(functionName);
        
        if (result) {
            successCount++;
        }
        
        // 添加延迟避免API限制
        await new Promise(resolve => setTimeout(resolve, 2000));
    }
    
    console.log('\n========================================');
    console.log(`🎉 HTTP触发器配置完成! 成功: ${successCount}/${functions.length}`);
    
    if (successCount > 0) {
        console.log('\n📋 访问信息:');
        console.log(`SCF控制台: https://console.cloud.tencent.com/scf/list?rid=1&ns=default`);
        console.log(`API网关控制台: https://console.cloud.tencent.com/apigateway`);
        
        console.log('\n📝 下一步:');
        console.log('1. 访问SCF控制台查看每个函数的触发器详情');
        console.log('2. 在触发器配置中复制实际的访问URL');
        console.log('3. 使用Postman或curl测试API端点');
        console.log('4. 根据需要配置自定义域名');
        console.log('5. 配置认证和限流策略');
        
        console.log('\n🔗 函数访问方式:');
        functions.forEach(func => {
            console.log(`  ${func}: https://console.cloud.tencent.com/scf/detail/1/default/${func}`);
        });
        
    } else {
        console.log('⚠️  没有触发器配置成功，请检查错误日志');
    }
}

// 运行配置
if (require.main === module) {
    setupHttpTriggers().catch(console.error);
}

module.exports = { setupHttpTriggers, addHttpTrigger }; 