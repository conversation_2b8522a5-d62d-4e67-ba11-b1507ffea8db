#!/usr/bin/env node

/**
 * 直接部署SCF函数的脚本
 * 使用腾讯云API直接部署，避免Serverless Framework的扫码问题
 */

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

// 腾讯云认证信息
const SECRET_ID = 'YOUR_SECRET_ID';
const SECRET_KEY = 'YOUR_SECRET_KEY';
const REGION = 'ap-guangzhou';

/**
 * 生成腾讯云API签名
 */
function generateSignature(method, host, path, params, secretKey) {
    const sortedParams = Object.keys(params)
        .sort()
        .map(key => `${key}=${params[key]}`)
        .join('&');
    
    const stringToSign = `${method}${host}${path}?${sortedParams}`;
    const signature = crypto
        .createHmac('sha1', secretKey)
        .update(stringToSign)
        .digest('base64');
    
    return signature;
}

/**
 * 调用腾讯云API
 */
async function callTencentCloudAPI(action, params = {}) {
    const host = 'scf.tencentcloudapi.com';
    const method = 'POST';
    const endpoint = `https://${host}/`;
    
    const timestamp = Math.floor(Date.now() / 1000);
    const nonce = Math.floor(Math.random() * 100000);
    
    const commonParams = {
        Action: action,
        Region: REGION,
        Timestamp: timestamp,
        Nonce: nonce,
        SecretId: SECRET_ID,
        Version: '2018-04-16'
    };
    
    const allParams = { ...commonParams, ...params };
    const signature = generateSignature(method, host, '/', allParams, SECRET_KEY);
    allParams.Signature = signature;
    
    const body = new URLSearchParams(allParams).toString();
    
    try {
        const response = await fetch(endpoint, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'Host': host
            },
            body: body
        });
        
        const result = await response.json();
        return result;
    } catch (error) {
        console.error('API调用失败:', error);
        throw error;
    }
}

/**
 * 创建或更新云函数
 */
async function deployFunction(functionName, functionCode, config = {}) {
    console.log(`🚀 部署函数: ${functionName}`);
    
    const defaultConfig = {
        Runtime: 'Nodejs18.15',
        Handler: 'index.main_handler',
        MemorySize: 512,
        Timeout: 900,
        Environment: {
            Variables: {
                NODE_ENV: 'production',
                ...config.Environment?.Variables
            }
        }
    };
    
    const finalConfig = { ...defaultConfig, ...config };
    
    try {
        // 尝试获取函数信息
        const getResult = await callTencentCloudAPI('GetFunction', {
            FunctionName: functionName
        });
        
        if (getResult.Response && !getResult.Response.Error) {
            // 函数存在，更新函数
            console.log(`📝 更新现有函数: ${functionName}`);
            
            const updateResult = await callTencentCloudAPI('UpdateFunctionCode', {
                FunctionName: functionName,
                ZipFile: functionCode
            });
            
            if (updateResult.Response?.Error) {
                console.error(`❌ 更新函数代码失败:`, updateResult.Response.Error);
                return false;
            }
            
            // 更新函数配置
            const configResult = await callTencentCloudAPI('UpdateFunctionConfiguration', {
                FunctionName: functionName,
                ...finalConfig
            });
            
            if (configResult.Response?.Error) {
                console.error(`❌ 更新函数配置失败:`, configResult.Response.Error);
                return false;
            }
            
        } else {
            // 函数不存在，创建新函数
            console.log(`✨ 创建新函数: ${functionName}`);
            
            const createResult = await callTencentCloudAPI('CreateFunction', {
                FunctionName: functionName,
                ZipFile: functionCode,
                ...finalConfig
            });
            
            if (createResult.Response?.Error) {
                console.error(`❌ 创建函数失败:`, createResult.Response.Error);
                return false;
            }
        }
        
        console.log(`✅ 函数 ${functionName} 部署成功`);
        return true;
        
    } catch (error) {
        console.error(`❌ 部署函数 ${functionName} 失败:`, error);
        return false;
    }
}

/**
 * 打包函数代码为Base64
 */
function packageFunction(functionPath) {
    try {
        const JSZip = require('jszip');
        const zip = new JSZip();
        
        // 读取函数文件
        const functionFile = path.join(functionPath, 'index.js');
        if (fs.existsSync(functionFile)) {
            const content = fs.readFileSync(functionFile, 'utf8');
            zip.file('index.js', content);
        }
        
        // 检查package.json
        const packageFile = path.join(functionPath, 'package.json');
        if (fs.existsSync(packageFile)) {
            const content = fs.readFileSync(packageFile, 'utf8');
            zip.file('package.json', content);
        }
        
        // 生成zip文件的base64
        const zipBuffer = zip.generateNodeStream({ type: 'nodebuffer', compression: 'DEFLATE' });
        return zipBuffer.toString('base64');
        
    } catch (error) {
        console.error('打包函数代码失败:', error);
        throw error;
    }
}

/**
 * 主部署流程
 */
async function main() {
    console.log('🚀 开始直接部署SCF函数...');
    console.log('========================================');
    
    const functions = [
        {
            name: 'resume-gateway',
            path: './functions/resume-gateway',
            config: {
                Description: '简历分析系统网关',
                MemorySize: 128,
                Timeout: 30
            }
        },
        {
            name: 'task-dispatcher',
            path: './functions/task-dispatcher',
            config: {
                Description: '任务分发器',
                MemorySize: 256,
                Timeout: 45
            }
        },
        {
            name: 'slice-worker',
            path: './functions/slice-worker',
            config: {
                Description: '核心AI处理器',
                MemorySize: 512,
                Timeout: 60
            }
        },
        {
            name: 'get-task-progress',
            path: './functions/get-task-progress',
            config: {
                Description: '进度查询',
                MemorySize: 128,
                Timeout: 15
            }
        },
        {
            name: 'result-aggregator',
            path: './functions/result-aggregator',
            config: {
                Description: '结果聚合器',
                MemorySize: 256,
                Timeout: 30
            }
        }
    ];
    
    let successCount = 0;
    
    for (const func of functions) {
        try {
            console.log(`\n📦 处理函数: ${func.name}`);
            
            if (!fs.existsSync(func.path)) {
                console.log(`⚠️  跳过不存在的函数: ${func.path}`);
                continue;
            }
            
            const zipCode = packageFunction(func.path);
            const success = await deployFunction(func.name, zipCode, func.config);
            
            if (success) {
                successCount++;
            }
            
            // 添加延迟避免API限制
            await new Promise(resolve => setTimeout(resolve, 2000));
            
        } catch (error) {
            console.error(`❌ 处理函数 ${func.name} 时出错:`, error);
        }
    }
    
    console.log('\n========================================');
    console.log(`🎉 部署完成! 成功: ${successCount}/${functions.length}`);
    
    if (successCount === functions.length) {
        console.log('✅ 所有函数部署成功！');
        
        // 输出访问信息
        console.log('\n📋 访问信息:');
        console.log(`地域: ${REGION}`);
        console.log('函数列表:');
        functions.forEach(func => {
            console.log(`  - ${func.name}: https://console.cloud.tencent.com/scf/detail/${REGION}/${func.name}`);
        });
        
    } else {
        console.log('⚠️  部分函数部署失败，请检查错误日志');
    }
}

// 检查依赖
try {
    require('jszip');
} catch (error) {
    console.log('📦 安装必要依赖...');
    require('child_process').execSync('npm install jszip', { stdio: 'inherit' });
}

// 检查fetch支持
if (typeof fetch === 'undefined') {
    global.fetch = require('node-fetch');
}

// 运行主程序
if (require.main === module) {
    main().catch(console.error);
}

module.exports = { deployFunction, callTencentCloudAPI }; 