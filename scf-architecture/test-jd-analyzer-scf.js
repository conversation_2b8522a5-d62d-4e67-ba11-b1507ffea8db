/**
 * 测试SCF版本的JD分析器
 * 直接调用SCF函数的handler方法进行测试
 */

// 设置环境变量 (必须在导入模块之前)
process.env.OPENROUTER_API_KEY = 'YOUR_OPENROUTER_API_KEY';
process.env.OPENROUTER_BASE_URL = 'https://openrouter.ai/api/v1';

const path = require('path');

// 导入JD分析器SCF函数
const { rawHandler } = require('./functions/jd-analyzer/index.js');

// 测试用的JD内容
const testJD = `
职位名称：智能客服产品运营专家
工作地点：北京
薪资：15-25K
经验要求：3-5年

职位描述：
负责智能客服产品的整体运营和优化工作，包括但不限于：

核心职责：
1. 负责智能客服机器人的语料库管理和知识图谱构建
2. 设计和优化对话流程，提升用户满意度和问题解决率
3. 分析客服对话数据，挖掘用户需求和产品改进点
4. 跨部门协作，推动产品功能迭代和用户体验优化
5. 制定智能客服运营策略，提升服务效率和质量

任职要求：
1. 本科及以上学历，计算机科学、人工智能、数据科学相关专业优先
2. 3-5年产品运营或AI产品相关工作经验
3. 熟悉自然语言处理(NLP)、机器学习等AI技术原理
4. 具备数据分析能力，熟练使用SQL、Python等工具
5. 有算法优化和模型调优经验者优先
6. 优秀的沟通协调能力和项目管理能力
7. 对人工智能和智能客服行业有深入理解和热情

加分项：
- 有大型互联网公司AI产品运营经验
- 有机器学习、深度学习项目实战经验
- 熟悉对话系统、知识图谱等相关技术
- 有数据挖掘和用户行为分析经验
`;

async function testJDAnalyzerSCF() {
  console.log('🧪 开始测试SCF版本的JD分析器...');
  console.log('📝 测试JD内容长度:', testJD.length);
  
  try {
    // 构造模拟的API Gateway事件
    const mockEvent = {
      httpMethod: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'test-client'
      },
      body: {
        jdContent: testJD,
        companyName: '测试公司',
        positionName: '智能客服产品运营专家'
      }
    };

    // 构造模拟的Lambda上下文
    const mockContext = {
      awsRequestId: 'test-request-' + Date.now(),
      getRemainingTimeInMillis: () => 60000
    };

    console.log('🚀 调用SCF handler...');
    const startTime = Date.now();
    
    const result = await rawHandler(mockEvent, mockContext);
    
    const duration = Date.now() - startTime;
    console.log(`⏱️ 执行时间: ${duration}ms`);
    
    console.log('✅ SCF函数调用成功');
    console.log('📊 返回结果:', JSON.stringify(result, null, 2));
    
    // 分析返回的数据结构
    if (result.statusCode === 200) {
      const responseData = typeof result.body === 'string' ? JSON.parse(result.body) : result.body;
      const analysisResult = responseData.data;
      
      console.log('\n📈 详细分析结果:');
      console.log('- HTTP状态码:', result.statusCode);
      console.log('- 响应代码:', responseData.code);
      console.log('- 消息:', responseData.message);
      console.log('- 核心能力数量:', analysisResult.coreAbilities?.length || 0);
      console.log('- 技术技能数量:', analysisResult.technicalSkills?.length || 0);
      console.log('- 软技能数量:', analysisResult.softSkills?.length || 0);
      console.log('- 经验级别:', analysisResult.experienceLevel);
      console.log('- 分析来源:', analysisResult.analysisSource);
      
      if (analysisResult.coreAbilities?.length > 0) {
        console.log('- 核心能力列表:', analysisResult.coreAbilities.join(', '));
      }
      
      if (analysisResult.technicalSkills?.length > 0) {
        console.log('- 技术技能列表:', analysisResult.technicalSkills.join(', '));
      }
      
      if (analysisResult.softSkills?.length > 0) {
        console.log('- 软技能列表:', analysisResult.softSkills.join(', '));
      }
      
      // 评估分析质量
      console.log('\n🎯 分析质量评估:');
      const expectedSkills = ['NLP', '自然语言处理', '机器学习', '算法', 'Python', 'SQL', '数据分析'];
      const foundSkills = [
        ...(analysisResult.coreAbilities || []), 
        ...(analysisResult.technicalSkills || []),
        ...(analysisResult.keywords || [])
      ];
      
      const matchedSkills = expectedSkills.filter(skill => 
        foundSkills.some(found => 
          found.toLowerCase().includes(skill.toLowerCase()) || 
          skill.toLowerCase().includes(found.toLowerCase())
        )
      );
      
      console.log('- 期望识别技能:', expectedSkills.join(', '));
      console.log('- 成功识别技能:', matchedSkills.join(', '));
      console.log('- 识别准确率:', `${((matchedSkills.length / expectedSkills.length) * 100).toFixed(1)}%`);
      
      if (analysisResult.analysisSource === 'ai') {
        console.log('✅ AI分析成功');
      } else {
        console.log('❌ AI分析失败，降级到规则分析');
      }
      
      // 验证经验级别是否正确
      if (analysisResult.experienceLevel === '中级') {
        console.log('✅ 经验级别识别正确 (3-5年 -> 中级)');
      } else {
        console.log(`❌ 经验级别识别错误: 期望"中级"，实际"${analysisResult.experienceLevel}"`);
      }
      
    } else {
      console.log('❌ SCF函数返回错误状态码:', result.statusCode);
      console.log('错误详情:', result.body);
    }
    
    return result;
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
    console.error('错误堆栈:', error.stack);
    return null;
  }
}

// 直接执行测试
console.log('🎬 启动SCF JD分析器测试...\n');

testJDAnalyzerSCF().then(result => {
  if (result && result.statusCode === 200) {
    console.log('\n🎉 SCF JD分析器测试成功完成');
    process.exit(0);
  } else {
    console.log('\n💥 SCF JD分析器测试失败');
    process.exit(1);
  }
}).catch(error => {
  console.error('\n💥 执行错误:', error);
  process.exit(1);
}); 