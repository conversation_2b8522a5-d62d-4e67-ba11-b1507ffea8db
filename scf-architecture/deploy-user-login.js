#!/usr/bin/env node

/**
 * 部署 user-login 云函数的专用脚本
 * 包含微信小程序环境变量配置
 */

const fs = require('fs');
const path = require('path');
const tencentcloud = require('tencentcloud-sdk-nodejs');

// 导入对应产品模块
const ScfClient = tencentcloud.scf.v20180416.Client;

// 腾讯云认证信息
const clientConfig = {
    credential: {
        secretId: process.env.TENCENT_SECRET_ID || 'YOUR_SECRET_ID',
        secretKey: process.env.TENCENT_SECRET_KEY || 'YOUR_SECRET_KEY'
    },
    region: 'ap-guangzhou',
    profile: {
        httpProfile: {
            endpoint: 'scf.tencentcloudapi.com'
        }
    }
};

const client = new ScfClient(clientConfig);

/**
 * 打包函数代码为Base64
 */
function packageFunction(functionPath) {
    try {
        const JSZip = require('jszip');
        const zip = new JSZip();
        
        // 读取函数文件
        const functionFile = path.join(functionPath, 'index.js');
        if (fs.existsSync(functionFile)) {
            const content = fs.readFileSync(functionFile, 'utf8');
            zip.file('index.js', content);
            console.log(`  📄 添加文件: index.js (${content.length} 字符)`);
        } else {
            throw new Error(`函数文件不存在: ${functionFile}`);
        }
        
        // 检查package.json
        const packageFile = path.join(functionPath, 'package.json');
        if (fs.existsSync(packageFile)) {
            const content = fs.readFileSync(packageFile, 'utf8');
            zip.file('package.json', content);
            console.log(`  📄 添加文件: package.json`);
        }
        
        // 读取shared目录中的文件（如果存在）
        const sharedPath = path.join(process.cwd(), 'shared');
        if (fs.existsSync(sharedPath)) {
            const sharedFiles = fs.readdirSync(sharedPath);
            sharedFiles.forEach(file => {
                const filePath = path.join(sharedPath, file);
                if (fs.statSync(filePath).isFile()) {
                    const content = fs.readFileSync(filePath, 'utf8');
                    zip.file(`shared/${file}`, content);
                    console.log(`  📄 添加共享文件: shared/${file}`);
                }
            });
        }
        
        return zip.generateAsync({ type: 'nodebuffer' });
    } catch (error) {
        console.error('函数打包失败:', error.message);
        throw error;
    }
}

/**
 * 创建或更新user-login云函数
 */
async function deployUserLoginFunction() {
    console.log(`\n🚀 部署函数: user-login`);
    
    const functionPath = path.join(__dirname, 'functions-new', 'userLogin');
    console.log(`📁 函数路径: ${functionPath}`);
    
    try {
        // 打包函数代码
        const zipBuffer = await packageFunction(functionPath);
        const zipBase64 = zipBuffer.toString('base64');
        console.log(`📦 代码包大小: ${Math.round(zipBuffer.length / 1024)} KB`);
        
        const functionConfig = {
            Runtime: 'Nodejs18.15',
            Handler: 'index.main',
            MemorySize: 512,
            Timeout: 60,
            Description: '微信小程序用户登录云函数 - 处理用户认证、openid获取和session管理',
            Environment: {
                Variables: [
                    { Key: 'NODE_ENV', Value: 'production' },
                    { Key: 'TENCENT_SECRET_ID', Value: clientConfig.credential.secretId },
                    { Key: 'TENCENT_SECRET_KEY', Value: clientConfig.credential.secretKey },
                    { Key: 'REGION', Value: 'ap-guangzhou' },
                    // 微信小程序配置
                    { Key: 'WECHAT_APPID', Value: 'wx2309e473610ea429' },
                    { Key: 'WECHAT_SECRET', Value: 'f8c2e8c1d4a3f6e9b2c7d5a8f1e4c9b6' },
                    // CloudBase配置
                    { Key: 'CLOUDBASE_ENV_ID', Value: 'prod-6gcfy0fq6022306f' },
                    { Key: 'CLOUDBASE_SECRET_ID', Value: clientConfig.credential.secretId },
                    { Key: 'CLOUDBASE_SECRET_KEY', Value: clientConfig.credential.secretKey },
                    // 日志配置
                    { Key: 'LOG_LEVEL', Value: 'info' }
                ]
            }
        };
        
        try {
            // 检查函数是否存在
            await client.GetFunction({ FunctionName: 'user-login' });
            
            // 函数存在，更新函数
            console.log(`📝 更新现有函数: user-login`);
            
            const updateCodeResult = await client.UpdateFunctionCode({
                FunctionName: 'user-login',
                ZipFile: zipBase64
            });
            
            if (updateCodeResult.Error) {
                console.error(`❌ 更新函数代码失败:`, updateCodeResult.Error);
                return false;
            }
            
            // 更新函数配置
            const updateConfigResult = await client.UpdateFunctionConfiguration({
                FunctionName: 'user-login',
                ...functionConfig
            });
            
            if (updateConfigResult.Error) {
                console.error(`❌ 更新函数配置失败:`, updateConfigResult.Error);
                return false;
            }
            
            console.log('✅ 函数更新成功');
            
        } catch (getError) {
            // 函数不存在，创建新函数
            console.log(`🆕 创建新函数: user-login`);
            
            const createResult = await client.CreateFunction({
                FunctionName: 'user-login',
                Code: { ZipFile: zipBase64 },
                ...functionConfig
            });
            
            if (createResult.Error) {
                console.error(`❌ 创建函数失败:`, createResult.Error);
                return false;
            }
            
            console.log('✅ 函数创建成功');
        }
        
        console.log('🎉 user-login 云函数部署完成!');
        return true;
        
    } catch (error) {
        console.error('❌ 部署失败:', error.message);
        return false;
    }
}

/**
 * 主函数
 */
async function main() {
    console.log('🚀 开始部署 user-login 云函数...');
    console.log('⏰ 部署时间:', new Date().toISOString());
    
    try {
        const success = await deployUserLoginFunction();
        
        if (success) {
            console.log('\n✅ 部署成功完成!');
            console.log('📝 函数名: user-login');
            console.log('🌏 区域: ap-guangzhou');
            console.log('🔧 运行时: Nodejs18.15');
            console.log('💾 内存: 512MB');
            console.log('⏱️  超时: 60秒');
        } else {
            console.log('\n❌ 部署失败!');
            process.exit(1);
        }
        
    } catch (error) {
        console.error('❌ 部署过程中发生错误:', error.message);
        process.exit(1);
    }
}

// 运行主函数
if (require.main === module) {
    main();
}

module.exports = { deployUserLoginFunction }; 