const tencentcloud = require("tencentcloud-sdk-nodejs");

// 腾讯云认证配置
const ApigwClient = tencentcloud.apigateway.v20180808.Client;
const ScfClient = tencentcloud.scf.v20180416.Client;

const clientConfig = {
    credential: {
        secretId: process.env.TENCENT_SECRET_ID || 'YOUR_SECRET_ID',
        secretKey: process.env.TENCENT_SECRET_KEY || 'YOUR_SECRET_KEY',
    },
    region: "ap-guangzhou",
    profile: {
        httpProfile: {
            endpoint: "apigateway.tencentcloudapi.com",
        },
    },
};

const scfConfig = {
    credential: clientConfig.credential,
    region: "ap-guangzhou",
    profile: {
        httpProfile: {
            endpoint: "scf.tencentcloudapi.com",
        },
    },
};

/**
 * 创建API网关服务
 */
async function createApiService() {
    const client = new ApigwClient(clientConfig);
    
    try {
        const params = {
            ServiceName: "resume-analysis-api",
            ServiceDesc: "简历分析系统API网关服务",
            Protocol: "https",
            ServiceType: "normal"
        };
        
        const result = await client.CreateService(params);
        console.log('✅ API网关服务创建成功:', result.ServiceId);
        return result.ServiceId;
    } catch (error) {
        if (error.code === 'UnsupportedOperation.ServiceAlreadyExist') {
            console.log('📋 API服务已存在，获取服务ID...');
            
            // 获取已存在的服务
            const listParams = {};
            const services = await client.DescribeServicesStatus(listParams);
            const existingService = services.Result.ServiceSet.find(s => s.ServiceName === 'resume-analysis-api');
            
            if (existingService) {
                console.log('✅ 找到已存在的API服务:', existingService.ServiceId);
                return existingService.ServiceId;
            }
        }
        throw error;
    }
}

/**
 * 为SCF函数创建API
 */
async function createApiForFunction(serviceId, functionName, apiPath, httpMethod = 'POST') {
    const client = new ApigwClient(clientConfig);
    
    try {
        const params = {
            ServiceId: serviceId,
            ServiceType: "SCF",
            RequestConfig: {
                Path: apiPath,
                Method: httpMethod,
            },
            ApiName: `${functionName}-api`,
            ApiDesc: `${functionName}函数的API接口`,
            ApiType: "NORMAL",
            AuthType: "NONE", // 无认证，实际使用时可以配置认证
            EnableCORS: true,
            ServiceConfig: {
                Method: httpMethod,
                Path: apiPath,
                Url: `https://service-${serviceId}-1234567890.ap-guangzhou.apigateway.myqcloud.com${apiPath}`,
                Product: "SCF",
                VpcConfig: {
                    UniqVpcId: "",
                    UniqSubnetId: ""
                }
            },
            ServiceScfFunctionName: functionName,
            ServiceScfFunctionNamespace: "default",
            ServiceScfFunctionQualifier: "$LATEST",
            ServiceScfIsIntegratedResponse: false
        };
        
        const result = await client.CreateApi(params);
        console.log(`✅ ${functionName} API创建成功:`, result.ApiId);
        return result.ApiId;
    } catch (error) {
        console.error(`❌ 创建${functionName} API失败:`, error.message);
        return null;
    }
}

/**
 * 为SCF函数添加API网关触发器
 */
async function addTriggerToFunction(functionName, serviceId, apiPath) {
    const client = new ScfClient(scfConfig);
    
    try {
        const params = {
            FunctionName: functionName,
            TriggerName: `${functionName}-trigger`,
            Type: "apigw",
            TriggerDesc: JSON.stringify({
                api: {
                    authRequired: "FALSE",
                    requestConfig: {
                        method: "POST"
                    },
                    isIntegratedResponse: "FALSE"
                },
                service: {
                    serviceId: serviceId
                },
                release: {
                    environmentName: "release"
                }
            })
        };
        
        const result = await client.CreateTrigger(params);
        console.log(`✅ ${functionName} 触发器创建成功`);
        return result;
    } catch (error) {
        if (error.code === 'ResourceInUse.Trigger') {
            console.log(`📋 ${functionName} 触发器已存在`);
            return true;
        }
        console.error(`❌ 创建${functionName}触发器失败:`, error.message);
        return null;
    }
}

/**
 * 发布API服务
 */
async function publishService(serviceId) {
    const client = new ApigwClient(clientConfig);
    
    try {
        const params = {
            ServiceId: serviceId,
            EnvironmentName: "release",
            ReleaseDesc: "简历分析系统API发布"
        };
        
        const result = await client.ReleaseService(params);
        console.log('✅ API服务发布成功');
        return result;
    } catch (error) {
        console.error('❌ API服务发布失败:', error.message);
        return null;
    }
}

/**
 * 主配置流程
 */
async function setupApiGateway() {
    console.log('🚀 开始配置API网关...');
    console.log('========================================');
    
    try {
        // 1. 创建API网关服务
        const serviceId = await createApiService();
        
        // 2. 为每个SCF函数创建API和触发器
        const functions = [
            { name: 'resume-gateway', path: '/resume/analyze' },
            { name: 'task-dispatcher', path: '/task/dispatch' },
            { name: 'slice-worker', path: '/slice/process' },
            { name: 'get-task-progress', path: '/task/progress' },
            { name: 'result-aggregator', path: '/result/aggregate' }
        ];
        
        for (const func of functions) {
            console.log(`\n🔧 配置函数: ${func.name}`);
            
            // 创建API
            const apiId = await createApiForFunction(serviceId, func.name, func.path);
            
            if (apiId) {
                // 添加触发器
                await addTriggerToFunction(func.name, serviceId, func.path);
                
                // 等待一下避免API限制
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
        }
        
        // 3. 发布API服务
        await publishService(serviceId);
        
        console.log('\n========================================');
        console.log('🎉 API网关配置完成！');
        console.log('\n📋 访问信息:');
        console.log(`API网关服务ID: ${serviceId}`);
        console.log(`API管理控制台: https://console.cloud.tencent.com/apigateway/service-detail/${serviceId}`);
        console.log('\n🔗 API端点:');
        
        functions.forEach(func => {
            console.log(`  ${func.name}: https://service-${serviceId}-1234567890.ap-guangzhou.apigateway.myqcloud.com${func.path}`);
        });
        
        console.log('\n📝 下一步:');
        console.log('1. 在API网关控制台查看服务状态');
        console.log('2. 使用Postman或curl测试API端点');
        console.log('3. 配置自定义域名（可选）');
        console.log('4. 配置认证和限流策略');
        
    } catch (error) {
        console.error('❌ API网关配置失败:', error);
    }
}

// 检查依赖
const requiredPackages = ['tencentcloud-sdk-nodejs'];
for (const pkg of requiredPackages) {
    try {
        require(pkg);
    } catch (error) {
        console.log(`📦 安装必要依赖: ${pkg}...`);
        require('child_process').execSync(`npm install ${pkg}`, { stdio: 'inherit' });
    }
}

// 运行配置
if (require.main === module) {
    setupApiGateway().catch(console.error);
}

module.exports = { setupApiGateway, createApiService, createApiForFunction }; 