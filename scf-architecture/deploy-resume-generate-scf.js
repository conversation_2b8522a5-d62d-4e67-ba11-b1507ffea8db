/**
 * Resume Generate SCF 直接部署脚本
 * 使用腾讯云SDK直接部署，避免Serverless Framework问题
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// 配置
const config = {
  // 腾讯云配置
  secretId: 'YOUR_SECRET_ID',
  secretKey: 'YOUR_SECRET_KEY',
  region: 'ap-guangzhou',
  
  // 函数配置
  functionName: 'resume-generate-simple',
  description: '简历生成器SCF版本（简化） - 智能简历优化和生成',
  handler: 'index-simple.main_handler',
  runtime: 'Nodejs18.15',
  memorySize: 512,
  timeout: 60,
  
  // 源代码路径
  srcPath: './functions/resume-generate',
  
  // API Gateway配置
  apiName: 'resume-generate-simple-api',
  apiDescription: '简历生成器API服务（简化版）'
};

/**
 * 创建ZIP部署包
 */
function createDeploymentPackage() {
  console.log('📦 创建部署包...');
  
  const srcPath = path.resolve(config.srcPath);
  const zipPath = path.resolve('./resume-generate-simple.zip');
  
  // 删除旧的ZIP文件
  if (fs.existsSync(zipPath)) {
    fs.unlinkSync(zipPath);
  }
  
  // 确保使用简化版本的文件
  const indexSimplePath = path.join(srcPath, 'index-simple.js');
  const packageSimplePath = path.join(srcPath, 'package-simple.json');
  
  if (!fs.existsSync(indexSimplePath)) {
    throw new Error('index-simple.js 不存在');
  }
  
  if (!fs.existsSync(packageSimplePath)) {
    throw new Error('package-simple.json 不存在');
  }
  
  // 创建临时目录
  const tempDir = path.resolve('./temp-resume-generate');
  if (fs.existsSync(tempDir)) {
    execSync(`rm -rf ${tempDir}`);
  }
  fs.mkdirSync(tempDir);
  
  try {
    // 复制必要文件
    fs.copyFileSync(indexSimplePath, path.join(tempDir, 'index-simple.js'));
    fs.copyFileSync(packageSimplePath, path.join(tempDir, 'package.json')); // 重命名为package.json
    
    // 创建ZIP文件
    execSync(`cd ${tempDir} && zip -r ${zipPath} .`);
    
    console.log('✅ 部署包创建成功:', zipPath);
    return zipPath;
    
  } finally {
    // 清理临时目录
    execSync(`rm -rf ${tempDir}`);
  }
}

/**
 * 部署函数
 */
async function deployFunction() {
  console.log('🚀 开始部署Resume Generate SCF函数...');
  console.log('⏰ 部署时间:', new Date().toISOString());
  
  try {
    // 创建部署包
    const zipPath = createDeploymentPackage();
    
    // 设置腾讯云认证
    process.env.TENCENTCLOUD_SECRET_ID = config.secretId;
    process.env.TENCENTCLOUD_SECRET_KEY = config.secretKey;
    process.env.TENCENTCLOUD_REGION = config.region;
    
    console.log('🔐 腾讯云认证配置完成');
    console.log('📍 区域:', config.region);
    console.log('🎯 函数名:', config.functionName);
    
    // 检查函数是否存在
    console.log('\n🔍 检查函数是否存在...');
    
    let functionExists = false;
    try {
      const checkResult = execSync(
        `tccli scf GetFunction --region ${config.region} --FunctionName ${config.functionName}`,
        { encoding: 'utf8', stdio: ['pipe', 'pipe', 'pipe'] }
      );
      const checkData = JSON.parse(checkResult);
      if (checkData.Response && !checkData.Response.Error) {
        functionExists = true;
        console.log('✅ 函数已存在，将进行更新');
      }
    } catch (error) {
      console.log('ℹ️  函数不存在，将创建新函数');
    }
    
    // 读取ZIP文件内容并转换为base64
    const zipContent = fs.readFileSync(zipPath);
    const base64Content = zipContent.toString('base64');
    
    if (functionExists) {
      // 更新函数代码
      console.log('\n⚡ 更新函数代码...');
      
      const updateCodeCmd = `tccli scf UpdateFunctionCode --region ${config.region} --FunctionName ${config.functionName} --ZipFile ${base64Content}`;
      
      const updateCodeResult = execSync(updateCodeCmd, { 
        encoding: 'utf8',
        maxBuffer: 50 * 1024 * 1024 // 50MB buffer
      });
      const updateCodeData = JSON.parse(updateCodeResult);
      
      if (updateCodeData.Response.Error) {
        throw new Error(`更新函数代码失败: ${updateCodeData.Response.Error.Message}`);
      }
      
      console.log('✅ 函数代码更新成功');
      
      // 更新函数配置
      console.log('⚙️  更新函数配置...');
      
      const updateConfigCmd = `tccli scf UpdateFunctionConfiguration --region ${config.region} --FunctionName ${config.functionName} --Handler ${config.handler} --Runtime ${config.runtime} --MemorySize ${config.memorySize} --Timeout ${config.timeout} --Description "${config.description}"`;
      
      const updateConfigResult = execSync(updateConfigCmd, { encoding: 'utf8' });
      const updateConfigData = JSON.parse(updateConfigResult);
      
      if (updateConfigData.Response.Error) {
        throw new Error(`更新函数配置失败: ${updateConfigData.Response.Error.Message}`);
      }
      
      console.log('✅ 函数配置更新成功');
      
    } else {
      // 创建新函数
      console.log('\n🆕 创建新函数...');
      
      const createCmd = `tccli scf CreateFunction --region ${config.region} --FunctionName ${config.functionName} --Handler ${config.handler} --Runtime ${config.runtime} --Code '{"ZipFile":"${base64Content}"}' --MemorySize ${config.memorySize} --Timeout ${config.timeout} --Description "${config.description}"`;
      
      const createResult = execSync(createCmd, { 
        encoding: 'utf8',
        maxBuffer: 50 * 1024 * 1024 // 50MB buffer
      });
      const createData = JSON.parse(createResult);
      
      if (createData.Response.Error) {
        throw new Error(`创建函数失败: ${createData.Response.Error.Message}`);
      }
      
      console.log('✅ 函数创建成功');
    }
    
    // 获取函数信息
    console.log('\n📊 获取函数信息...');
    
    const getInfoResult = execSync(
      `tccli scf GetFunction --region ${config.region} --FunctionName ${config.functionName}`,
      { encoding: 'utf8' }
    );
    const infoData = JSON.parse(getInfoResult);
    
    if (infoData.Response.Error) {
      throw new Error(`获取函数信息失败: ${infoData.Response.Error.Message}`);
    }
    
    const functionInfo = infoData.Response;
    
    console.log('\n🎉 Resume Generate SCF 部署成功!');
    console.log('📝 函数信息:');
    console.log('  - 函数名称:', functionInfo.Configuration.FunctionName);
    console.log('  - 函数描述:', functionInfo.Configuration.Description);
    console.log('  - 运行时:', functionInfo.Configuration.Runtime);
    console.log('  - 处理程序:', functionInfo.Configuration.Handler);
    console.log('  - 内存大小:', functionInfo.Configuration.MemorySize, 'MB');
    console.log('  - 超时时间:', functionInfo.Configuration.Timeout, '秒');
    console.log('  - 修改时间:', functionInfo.Configuration.ModTime);
    
    // 清理部署包
    fs.unlinkSync(zipPath);
    console.log('\n🗑️  清理临时文件完成');
    
    console.log('\n✅ 部署完成! Resume Generate SCF函数已成功部署到腾讯云。');
    console.log('🔗 可以通过API Gateway调用此函数进行简历生成。');
    
    return {
      success: true,
      functionName: config.functionName,
      region: config.region,
      functionInfo: functionInfo.Configuration
    };
    
  } catch (error) {
    console.error('\n❌ 部署失败:', error.message);
    console.error('🔍 错误详情:', error);
    
    return {
      success: false,
      error: error.message
    };
  }
}

// 运行部署
if (require.main === module) {
  deployFunction().then(result => {
    if (result.success) {
      console.log('\n🎊 Resume Generate SCF部署流程完成!');
      process.exit(0);
    } else {
      console.log('\n💥 Resume Generate SCF部署失败!');
      process.exit(1);
    }
  }).catch(error => {
    console.error('\n🚨 部署过程中出现未知错误:', error);
    process.exit(1);
  });
}

module.exports = { deployFunction }; 