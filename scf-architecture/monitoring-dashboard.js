#!/usr/bin/env node

/**
 * 监控仪表板 - 查看SCF函数性能和日志统计
 * 零依赖，专为腾讯云SCF设计
 */

const tencentcloud = require("tencentcloud-sdk-nodejs");

// 腾讯云SCF客户端配置
const ScfClient = tencentcloud.scf.v20180416.Client;
const ClsClient = tencentcloud.cls.v20201016.Client;

const clientConfig = {
    credential: {
        secretId: process.env.TENCENT_SECRET_ID || 'YOUR_SECRET_ID',
        secretKey: process.env.TENCENT_SECRET_KEY || 'YOUR_SECRET_KEY',
    },
    region: "ap-guangzhou",
    profile: {
        httpProfile: {
            endpoint: "scf.tencentcloudapi.com",
        },
    },
};

/**
 * 获取函数运行统计
 */
async function getFunctionMetrics(functionName, timeRange = '1h') {
    const client = new ScfClient(clientConfig);
    
    try {
        console.log(`📊 获取函数 ${functionName} 的运行指标...`);
        
        // 获取函数基本信息
        const functionInfo = await client.GetFunction({
            FunctionName: functionName
        });
        
        const endTime = new Date();
        const startTime = new Date(endTime.getTime() - (timeRange === '1h' ? 3600000 : 86400000));
        
        console.log(`📈 ${functionName} 运行状态:`);
        console.log(`   📍 状态: ${functionInfo.Configuration.State}`);
        console.log(`   ⏱️  超时设置: ${functionInfo.Configuration.Timeout}秒`);
        console.log(`   💾 内存配置: ${functionInfo.Configuration.MemorySize}MB`);
        console.log(`   📝 描述: ${functionInfo.Configuration.Description}`);
        console.log(`   🔄 更新时间: ${functionInfo.Configuration.ModTime}`);
        
        return {
            name: functionName,
            state: functionInfo.Configuration.State,
            timeout: functionInfo.Configuration.Timeout,
            memory: functionInfo.Configuration.MemorySize,
            lastModified: functionInfo.Configuration.ModTime
        };
        
    } catch (error) {
        console.error(`❌ 获取函数 ${functionName} 指标失败:`, error.message);
        return {
            name: functionName,
            error: error.message
        };
    }
}

/**
 * 实时监控所有函数
 */
async function monitorAllFunctions() {
    console.log('🔍 AI简历分析系统 - 实时监控仪表板');
    console.log('==========================================');
    console.log(`📅 监控时间: ${new Date().toLocaleString()}`);
    console.log('==========================================\n');
    
    const functions = [
        'resume-gateway',
        'task-dispatcher', 
        'chunk-processor',
        'result-aggregator',
        'resume-generate'
    ];
    
    const metrics = [];
    
    for (const funcName of functions) {
        const metric = await getFunctionMetrics(funcName);
        metrics.push(metric);
        console.log(''); // 换行
    }
    
    // 生成统计摘要
    console.log('==========================================');
    console.log('📊 系统监控摘要');
    console.log('==========================================');
    
    const healthyFunctions = metrics.filter(m => !m.error && m.state === 'Active');
    const errorFunctions = metrics.filter(m => m.error || m.state !== 'Active');
    
    console.log(`✅ 健康函数: ${healthyFunctions.length}/${metrics.length}`);
    console.log(`❌ 异常函数: ${errorFunctions.length}/${metrics.length}`);
    
    if (healthyFunctions.length > 0) {
        const avgTimeout = Math.round(healthyFunctions.reduce((sum, f) => sum + f.timeout, 0) / healthyFunctions.length);
        const avgMemory = Math.round(healthyFunctions.reduce((sum, f) => sum + f.memory, 0) / healthyFunctions.length);
        
        console.log(`⏱️  平均超时设置: ${avgTimeout}秒`);
        console.log(`💾 平均内存配置: ${avgMemory}MB`);
    }
    
    if (errorFunctions.length > 0) {
        console.log('\n⚠️  需要关注的函数:');
        errorFunctions.forEach(f => {
            console.log(`   - ${f.name}: ${f.error || f.state}`);
        });
    }
    
    console.log('\n🎯 性能建议:');
    
    // 超时时间分析
    const highTimeoutFunctions = healthyFunctions.filter(f => f.timeout > 600);
    const lowTimeoutFunctions = healthyFunctions.filter(f => f.timeout < 60);
    
    if (highTimeoutFunctions.length > 0) {
        console.log(`   📈 高超时函数 (>600s): ${highTimeoutFunctions.map(f => f.name).join(', ')}`);
        console.log(`      - 建议: 监控实际执行时间，考虑优化或调整超时设置`);
    }
    
    if (lowTimeoutFunctions.length > 0) {
        console.log(`   📉 低超时函数 (<60s): ${lowTimeoutFunctions.map(f => f.name).join(', ')}`);
        console.log(`      - 建议: 确保超时时间足够处理高负载情况`);
    }
    
    // 内存配置分析
    const highMemoryFunctions = healthyFunctions.filter(f => f.memory > 512);
    const lowMemoryFunctions = healthyFunctions.filter(f => f.memory < 256);
    
    if (highMemoryFunctions.length > 0) {
        console.log(`   🧠 高内存函数 (>512MB): ${highMemoryFunctions.map(f => f.name).join(', ')}`);
        console.log(`      - 建议: 监控内存使用率，优化内存效率`);
    }
    
    if (lowMemoryFunctions.length > 0) {
        console.log(`   💽 低内存函数 (<256MB): ${lowMemoryFunctions.map(f => f.name).join(', ')}`);
        console.log(`      - 建议: 监控是否出现内存不足错误`);
    }
    
    return metrics;
}

/**
 * 性能压力测试
 */
async function performanceTest() {
    console.log('🚀 开始性能压力测试...');
    console.log('==========================================');
    
    const testData = {
        'resume-gateway': {
            httpMethod: 'POST',
            path: '/resume/analyze',
            body: JSON.stringify({
                resumeText: "测试简历内容，包含JavaScript、React、3年开发经验等关键词",
                userId: "perf-test-user"
            })
        },
        'task-dispatcher': {
            taskId: 'perf-test-001',
            content: '性能测试简历内容，包含多种技能和经验描述，用于验证系统处理能力和响应时间',
            chunkSize: 100
        },
        'chunk-processor': {
            taskId: 'perf-test-001',
            sliceId: 'perf-slice-001',
            content: '前端开发工程师，5年JavaScript开发经验，精通React、Vue等框架'
        },
        'result-aggregator': {
            taskId: 'perf-test-001',
            results: [
                { sliceId: 'slice-1', analysis: { skills: ['JavaScript', 'React'], experience: ['5年经验'] } },
                { sliceId: 'slice-2', analysis: { skills: ['Vue', 'TypeScript'], experience: ['前端开发'] } }
            ]
        },
        'resume-generate': {
            taskId: 'perf-test-001',
            analysisResults: {
                skills: ['JavaScript', 'React', 'Vue', 'TypeScript'],
                experience: ['5年前端开发经验'],
                education: ['计算机科学学士'],
                projects: ['电商平台开发']
            }
        }
    };
    
    const testFunctions = require('./test-functions');
    const results = [];
    
    for (const [functionName, payload] of Object.entries(testData)) {
        console.log(`\n🔥 测试函数: ${functionName}`);
        
        // 执行多次测试
        const iterations = 3;
        const times = [];
        
        for (let i = 0; i < iterations; i++) {
            const startTime = Date.now();
            try {
                const result = await testFunctions.invokeFunction(functionName, payload);
                const duration = Date.now() - startTime;
                times.push(duration);
                
                console.log(`   第${i + 1}次: ${duration}ms ${result.success ? '✅' : '❌'}`);
            } catch (error) {
                console.log(`   第${i + 1}次: 失败 ❌ - ${error.message}`);
                times.push(null);
            }
        }
        
        const validTimes = times.filter(t => t !== null);
        const avgTime = validTimes.length > 0 ? Math.round(validTimes.reduce((a, b) => a + b, 0) / validTimes.length) : 0;
        const minTime = validTimes.length > 0 ? Math.min(...validTimes) : 0;
        const maxTime = validTimes.length > 0 ? Math.max(...validTimes) : 0;
        const successRate = Math.round((validTimes.length / iterations) * 100);
        
        results.push({
            function: functionName,
            avgTime,
            minTime,
            maxTime,
            successRate,
            iterations
        });
        
        console.log(`   📊 平均: ${avgTime}ms | 最快: ${minTime}ms | 最慢: ${maxTime}ms | 成功率: ${successRate}%`);
    }
    
    console.log('\n==========================================');
    console.log('🎯 性能测试总结');
    console.log('==========================================');
    
    results.forEach(result => {
        const status = result.successRate === 100 ? '🟢' : result.successRate >= 50 ? '🟡' : '🔴';
        console.log(`${status} ${result.function}: ${result.avgTime}ms平均 | ${result.successRate}%成功率`);
    });
    
    const overallAvg = Math.round(results.reduce((sum, r) => sum + r.avgTime, 0) / results.length);
    const overallSuccess = Math.round(results.reduce((sum, r) => sum + r.successRate, 0) / results.length);
    
    console.log(`\n🎖️  系统整体性能: ${overallAvg}ms平均响应 | ${overallSuccess}%整体成功率`);
    
    return results;
}

/**
 * 日志分析
 */
function analyzeLoggingFeatures() {
    console.log('📝 日志和监控功能分析');
    console.log('==========================================');
    
    console.log('✅ 已实现的日志功能:');
    console.log('   📊 结构化JSON日志格式');
    console.log('   🕐 时间戳和请求ID跟踪');
    console.log('   🎯 函数级别和操作级别计时');
    console.log('   💾 内存使用监控');
    console.log('   📈 性能指标收集');
    console.log('   ❌ 错误分类和堆栈跟踪');
    console.log('   🔐 敏感数据过滤');
    console.log('   📋 上下文信息记录');
    
    console.log('\n🎛️  可用的日志级别:');
    console.log('   🔴 ERROR: 错误和异常信息');
    console.log('   🟡 WARN:  警告和潜在问题');
    console.log('   🔵 INFO:  一般信息和状态更新');
    console.log('   ⚪ DEBUG: 详细的调试信息');
    
    console.log('\n📊 性能监控指标:');
    console.log('   ⏱️  执行时间测量 (毫秒级精度)');
    console.log('   💽 内存使用统计 (RSS, Heap, External)');
    console.log('   📥 请求计数和错误率');
    console.log('   🎯 成功率和失败分析');
    
    console.log('\n🔗 腾讯云集成:');
    console.log('   📝 自动关联SCF请求ID');
    console.log('   🏷️  函数名称和版本信息');
    console.log('   ⏰ 剩余执行时间监控');
    console.log('   🔧 配置信息记录');
    
    console.log('\n💡 使用建议:');
    console.log('   1. 在腾讯云控制台查看结构化日志');
    console.log('   2. 使用CLS日志服务进行日志搜索和分析');
    console.log('   3. 设置基于日志的监控告警');
    console.log('   4. 定期查看性能指标趋势');
    console.log('   5. 根据错误日志进行问题诊断');
}

/**
 * 主菜单
 */
async function showMenu() {
    console.log('\n🎛️  AI简历分析系统 - 监控仪表板');
    console.log('==========================================');
    console.log('1. 📊 查看所有函数状态');
    console.log('2. 🚀 执行性能压力测试');
    console.log('3. 📝 日志功能说明');
    console.log('4. 🔄 实时监控 (每30秒刷新)');
    console.log('5. 🚪 退出');
    console.log('==========================================');
}

/**
 * 实时监控模式
 */
async function realTimeMonitoring() {
    console.log('🔄 启动实时监控模式 (Ctrl+C 退出)...\n');
    
    const monitorInterval = setInterval(async () => {
        console.clear();
        console.log('🔄 实时监控中... (Ctrl+C 退出)');
        console.log('==========================================\n');
        
        await monitorAllFunctions();
        
        console.log('\n⏰ 30秒后自动刷新...');
    }, 30000);
    
    // 立即执行一次
    await monitorAllFunctions();
    console.log('\n⏰ 30秒后自动刷新...');
    
    // 处理退出信号
    process.on('SIGINT', () => {
        clearInterval(monitorInterval);
        console.log('\n\n👋 监控已停止');
        process.exit(0);
    });
}

/**
 * 主程序
 */
async function main() {
    const args = process.argv.slice(2);
    
    if (args.length === 0) {
        // 交互模式
        const readline = require('readline');
        const rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout
        });
        
        while (true) {
            await showMenu();
            
            const choice = await new Promise(resolve => {
                rl.question('请选择操作 (1-5): ', resolve);
            });
            
            console.log('');
            
            switch (choice) {
                case '1':
                    await monitorAllFunctions();
                    break;
                case '2':
                    await performanceTest();
                    break;
                case '3':
                    analyzeLoggingFeatures();
                    break;
                case '4':
                    rl.close();
                    await realTimeMonitoring();
                    return;
                case '5':
                    console.log('👋 再见!');
                    rl.close();
                    return;
                default:
                    console.log('❌ 无效选择，请重试');
            }
            
            console.log('\n按回车键继续...');
            await new Promise(resolve => rl.question('', resolve));
        }
    } else {
        // 命令行模式
        const command = args[0];
        
        switch (command) {
            case 'status':
                await monitorAllFunctions();
                break;
            case 'test':
                await performanceTest();
                break;
            case 'logs':
                analyzeLoggingFeatures();
                break;
            case 'monitor':
                await realTimeMonitoring();
                break;
            default:
                console.log('📖 使用说明:');
                console.log('  监控面板: node monitoring-dashboard.js');
                console.log('  查看状态: node monitoring-dashboard.js status');
                console.log('  性能测试: node monitoring-dashboard.js test');
                console.log('  日志说明: node monitoring-dashboard.js logs');
                console.log('  实时监控: node monitoring-dashboard.js monitor');
        }
    }
}

// 运行主程序
if (require.main === module) {
    main().catch(error => {
        console.error('❌ 程序执行错误:', error);
        process.exit(1);
    });
}

module.exports = { 
    getFunctionMetrics, 
    monitorAllFunctions, 
    performanceTest, 
    analyzeLoggingFeatures 
}; 