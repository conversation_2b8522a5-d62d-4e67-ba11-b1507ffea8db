/**
 * <PERSON> Tsui简历测试 - SCF函数集成测试
 * 使用真实的简历数据测试所有SCF函数
 */

const tencentcloud = require("tencentcloud-sdk-nodejs");
const fs = require('fs');
const path = require('path');

// 腾讯云SCF客户端配置
const ScfClient = tencentcloud.scf.v20180416.Client;

const clientConfig = {
    credential: {
        secretId: 'YOUR_SECRET_ID',
        secretKey: 'YOUR_SECRET_KEY',
    },
    region: "ap-guangzhou",
    profile: {
        httpProfile: {
            endpoint: "scf.tencentcloudapi.com",
        },
    },
};

// <PERSON>简历数据 (基于提供的简历内容)
const IAN_TSUI_RESUME = {
    personalInfo: {
        name: "徐瑜泽 (<PERSON>)",
        email: "<EMAIL>",
        phone: "+86 138-0000-0000",
        linkedin: "linkedin.com/in/iantsui",
        location: "上海, 中国"
    },
    summary: `资深亚马逊客户经理，具有6年以上国际电商平台运营经验。专注于中国市场开发和客户关系管理，成功帮助500+海外品牌进入中国市场。精通跨境电商策略、数据分析和团队管理。`,
    
    experience: [
        {
            title: "高级客户经理",
            company: "亚马逊 (Amazon)",
            location: "上海",
            duration: "2020.03 - 至今",
            responsibilities: [
                "管理50+重点客户的亚马逊中国业务，年GMV超过2000万美元",
                "设计并实施跨境电商策略，客户平均销售增长达85%",
                "负责新客户开发和市场拓展，成功签约30+国际品牌",
                "协调跨部门团队，优化客户服务流程，客户满意度达95%"
            ],
            achievements: [
                "连续3年获得年度最佳客户经理奖项",
                "开发的客户关系管理系统被公司全球推广使用",
                "培训新入职员工200+人，团队绩效提升40%"
            ]
        },
        {
            title: "电商运营专员",
            company: "天猫国际",
            location: "杭州",
            duration: "2018.07 - 2020.02",
            responsibilities: [
                "负责20+国际品牌的天猫国际店铺运营",
                "制定并执行营销策略，店铺流量增长150%",
                "分析市场数据，优化商品定价和库存管理",
                "与供应商和物流团队协调，确保订单及时履行"
            ]
        }
    ],
    
    education: [
        {
            degree: "商务管理学士",
            university: "上海财经大学",
            location: "上海",
            duration: "2014.09 - 2018.06",
            gpa: "3.7/4.0",
            honors: ["优秀毕业生", "学术奖学金获得者"]
        }
    ],
    
    skills: {
        technical: [
            "数据分析 (Excel, SQL, Tableau)",
            "CRM系统 (Salesforce, HubSpot)",
            "电商平台 (Amazon, Tmall, JD)",
            "项目管理工具 (Jira, Trello)"
        ],
        languages: [
            "中文 (母语)",
            "英语 (流利 - TOEFL 105)",
            "日语 (基础会话)"
        ],
        business: [
            "跨境电商策略",
            "客户关系管理",
            "数据分析与报告",
            "团队管理与培训",
            "市场营销策划"
        ]
    },
    
    certifications: [
        {
            name: "亚马逊认证解决方案架构师",
            issuer: "Amazon Web Services",
            date: "2021.08"
        },
        {
            name: "Google Analytics认证",
            issuer: "Google",
            date: "2020.12"
        }
    ],
    
    projects: [
        {
            name: "智能客户推荐系统",
            description: "基于机器学习的客户行为分析系统，提升客户匹配精度65%",
            technologies: ["Python", "scikit-learn", "AWS"],
            impact: "节省客户开发成本30%，提升转化率45%"
        },
        {
            name: "跨境电商数据仪表板",
            description: "实时监控多平台销售数据的可视化系统",
            technologies: ["Tableau", "SQL", "API集成"],
            impact: "数据分析效率提升80%，决策响应时间缩短50%"
        }
    ]
};

// 将简历转换为文本格式
function resumeToText(resume) {
    let text = `${resume.personalInfo.name}\n`;
    text += `邮箱: ${resume.personalInfo.email}\n`;
    text += `电话: ${resume.personalInfo.phone}\n`;
    text += `LinkedIn: ${resume.personalInfo.linkedin}\n`;
    text += `地址: ${resume.personalInfo.location}\n\n`;
    
    text += `个人简介:\n${resume.summary}\n\n`;
    
    text += `工作经历:\n`;
    resume.experience.forEach((exp, index) => {
        text += `${index + 1}. ${exp.title} - ${exp.company} (${exp.duration})\n`;
        text += `   地点: ${exp.location}\n`;
        text += `   主要职责:\n`;
        exp.responsibilities.forEach(resp => {
            text += `   • ${resp}\n`;
        });
        if (exp.achievements) {
            text += `   主要成就:\n`;
            exp.achievements.forEach(achievement => {
                text += `   • ${achievement}\n`;
            });
        }
        text += `\n`;
    });
    
    text += `教育背景:\n`;
    resume.education.forEach((edu, index) => {
        text += `${index + 1}. ${edu.degree} - ${edu.university} (${edu.duration})\n`;
        text += `   GPA: ${edu.gpa}\n`;
        text += `   荣誉: ${edu.honors.join(', ')}\n\n`;
    });
    
    text += `技能:\n`;
    text += `技术技能: ${resume.skills.technical.join(', ')}\n`;
    text += `语言技能: ${resume.skills.languages.join(', ')}\n`;
    text += `商务技能: ${resume.skills.business.join(', ')}\n\n`;
    
    text += `认证:\n`;
    resume.certifications.forEach((cert, index) => {
        text += `${index + 1}. ${cert.name} - ${cert.issuer} (${cert.date})\n`;
    });
    
    text += `\n项目经验:\n`;
    resume.projects.forEach((project, index) => {
        text += `${index + 1}. ${project.name}\n`;
        text += `   描述: ${project.description}\n`;
        text += `   技术栈: ${project.technologies.join(', ')}\n`;
        text += `   影响: ${project.impact}\n\n`;
    });
    
    return text;
}

// 测试用的JD数据 (智能客服产品运营专家)
const SAMPLE_JD = {
    title: "智能客服产品运营专家",
    company: "某互联网公司",
    location: "北京",
    jobId: "JVJWV",
    employmentType: "正式",
    department: "运营 - 产品运营",
    description: `
职位描述
1、理解和熟悉算法运作原理，基于业务现状，进行问题分类并设计该类问题场景的业务解决方案，能推动落地到算法团队进行符合业务诉求的匹配模型上线和调试，对模型效果进行验收、持续优化；
2、通过监控渠道的运营状况，对业务和服务变化进行及时的服务策略设计和部署，建立运营保障体系；
3、负责智能知识库体系建设，优化机器人解决方案的表达形式、表达文案、对话流程设计；持续推动方案层的内容优化；
4、负责分析智能侧服务需求，优化业务流程和产品功能，提升智能服务能力；
5、对整体智能对话质量负责，搭建全链路的对话数据监控体系，并能沉淀基于业务现状的运营经验和方法。

职位要求
1、3年以上智能客服工作经验；
2、对智能服务和NLP领域有较高的热情和好奇心，对智能解决方案有较深理解；
3、能够利用AI能力为人工提效，具有一定的智能服务领域前瞻性；
4、具备良好的数据分析、对标分析能力；
5、具备算法模型0-1的上线经历，有模型替换、多模型运作经验尤佳。
`,
    requirements: [
        "3年以上智能客服工作经验",
        "NLP领域深度理解",
        "算法运作原理熟悉",
        "智能服务解决方案经验",
        "数据分析、对标分析能力",
        "算法模型0-1上线经历",
        "多模型运作经验"
    ],
    keySkills: [
        "算法模型",
        "NLP技术",
        "智能客服",
        "数据分析",
        "产品运营",
        "对话流程设计",
        "知识库建设"
    ]
};

/**
 * 调用SCF函数
 */
async function invokeFunction(functionName, payload = {}) {
    const client = new ScfClient(clientConfig);
    
    try {
        const params = {
            FunctionName: functionName,
            InvocationType: "RequestResponse",
            Qualifier: "$LATEST",
            ClientContext: JSON.stringify(payload)
        };
        
        console.log(`🔄 调用函数: ${functionName}`);
        console.log(`📤 请求参数大小: ${JSON.stringify(payload).length} 字符`);
        
        const startTime = Date.now();
        const result = await client.Invoke(params);
        const endTime = Date.now();
        
        let response;
        try {
            response = JSON.parse(result.Result.RetMsg);
        } catch (e) {
            response = result.Result.RetMsg;
        }
        
        const isSuccess = !result.Result.ErrMsg && 
                         (!response.errorCode || response.errorCode === 0) &&
                         (!response.statusCode || response.statusCode === 200);
        
        console.log(`${isSuccess ? '✅' : '❌'} 调用${isSuccess ? '成功' : '失败'}!`);
        console.log(`⏱️  网络+执行时间: ${endTime - startTime}ms`);
        console.log(`🏃 函数执行时间: ${result.Result.Duration}ms`);
        console.log(`💾 内存使用: ${result.Result.MemUsage}KB`);
        
        if (result.Result.ErrMsg) {
            console.log(`❌ 错误信息: ${result.Result.ErrMsg}`);
        }
        
        if (response && typeof response === 'object') {
            console.log(`📊 响应数据类型: ${typeof response}`);
            if (response.data) {
                console.log(`📦 响应数据大小: ${JSON.stringify(response.data).length} 字符`);
            }
        }
        
        return {
            success: isSuccess,
            result: response,
            duration: result.Result.Duration,
            memUsage: result.Result.MemUsage,
            errorMsg: result.Result.ErrMsg,
            networkTime: endTime - startTime
        };
        
    } catch (error) {
        console.error(`❌ 调用函数 ${functionName} 失败:`, error.message);
        return {
            success: false,
            error: error.message
        };
    }
}

/**
 * 测试完整的简历分析流程
 */
async function testCompleteResumeAnalysis() {
    console.log('🚀 开始Ian Tsui简历完整分析测试...');
    console.log('=' * 60);
    
    const resumeText = resumeToText(IAN_TSUI_RESUME);
    const jdText = `${SAMPLE_JD.title}\n${SAMPLE_JD.location}\n${SAMPLE_JD.department}\n职位ID：${SAMPLE_JD.jobId}\n\n${SAMPLE_JD.description}`;
    
    console.log(`📄 简历文本长度: ${resumeText.length} 字符`);
    console.log(`📋 JD文本长度: ${jdText.length} 字符`);
    
    console.log(`\n🎯 测试场景: Ian Tsui (Amazon客户经理) vs 智能客服产品运营专家职位`);
    console.log(`📊 预期结果: 应该识别出技能不匹配，经验领域差异大`);
    
    const testResults = [];
    const testId = `test-${Date.now()}`;
    
    // 测试1: resume-gateway - 简历入口分析
    console.log('\n' + '='.repeat(50));
    console.log('🔍 测试1: 简历入口分析 (resume-gateway)');
    console.log('='.repeat(50));
    
    const gatewayResult = await invokeFunction('resume-gateway', {
        httpMethod: 'POST',
        path: '/resume/analyze',
        headers: {
            'Content-Type': 'application/json',
            'X-User-Id': 'ian-tsui-test'
        },
        body: JSON.stringify({
            resumeText: resumeText,
            userId: 'ian-tsui-test',
            analysis: {
                skills: true,
                experience: true,
                education: true,
                projects: true
            }
        })
    });
    
    testResults.push({
        step: 1,
        function: 'resume-gateway',
        ...gatewayResult
    });
    
    // 测试2: task-dispatcher - 任务分发
    console.log('\n' + '='.repeat(50));
    console.log('🎯 测试2: 任务分发 (task-dispatcher)');
    console.log('='.repeat(50));
    
    const dispatcherResult = await invokeFunction('task-dispatcher', {
        taskId: testId,
        resumeContent: resumeText,
        chunkSize: 1000,
        analysisType: 'comprehensive',
        userId: 'ian-tsui-test'
    });
    
    testResults.push({
        step: 2,
        function: 'task-dispatcher',
        ...dispatcherResult
    });
    
    // 测试3: chunk-processor - 分块处理
    console.log('\n' + '='.repeat(50));
    console.log('🔧 测试3: 分块处理 (chunk-processor)');
    console.log('='.repeat(50));
    
    const processorResult = await invokeFunction('chunk-processor', {
        taskId: testId,
        sliceId: `${testId}-slice-1`,
        content: resumeText.substring(0, 1000),
        analysisType: 'skills_experience',
        context: {
            fullResumeLength: resumeText.length,
            chunkIndex: 0,
            totalChunks: 3
        }
    });
    
    testResults.push({
        step: 3,
        function: 'chunk-processor',
        ...processorResult
    });
    
    // 测试4: result-aggregator - 结果聚合
    console.log('\n' + '='.repeat(50));
    console.log('📊 测试4: 结果聚合 (result-aggregator)');
    console.log('='.repeat(50));
    
    const aggregatorResult = await invokeFunction('result-aggregator', {
        taskId: testId,
        results: [
            {
                sliceId: `${testId}-slice-1`,
                analysis: {
                    skills: ["客户管理", "数据分析", "跨境电商", "团队管理"],
                    experience: "6年以上亚马逊客户经理经验",
                    strengths: ["客户关系管理", "业务增长", "团队领导"]
                }
            },
            {
                sliceId: `${testId}-slice-2`,
                analysis: {
                    skills: ["CRM系统", "Salesforce", "数据可视化"],
                    experience: "天猫国际运营经验",
                    projects: ["智能客户推荐系统", "数据仪表板"]
                }
            }
        ],
        userId: 'ian-tsui-test'
    });
    
    testResults.push({
        step: 4,
        function: 'result-aggregator',
        ...aggregatorResult
    });
    
    // 测试5: resume-generate - 简历生成
    console.log('\n' + '='.repeat(50));
    console.log('📝 测试5: 简历生成 (resume-generate)');
    console.log('='.repeat(50));
    
    const generateResult = await invokeFunction('resume-generate', {
        taskId: testId,
        analysisResults: {
            skills: ["客户管理", "数据分析", "跨境电商", "CRM系统", "团队管理"],
            experience: "6年以上国际电商和客户管理经验",
            strengths: ["客户关系管理", "业务增长策略", "团队领导"],
            recommendations: [
                "突出数据分析能力",
                "强调客户增长成果",
                "展示团队管理经验"
            ]
        },
        targetJD: {
            title: SAMPLE_JD.title,
            requirements: SAMPLE_JD.requirements,
            company: SAMPLE_JD.company
        },
        userId: 'ian-tsui-test'
    });
    
    testResults.push({
        step: 5,
        function: 'resume-generate',
        ...generateResult
    });
    
    // 生成测试报告
    console.log('\n' + '='.repeat(60));
    console.log('📋 Ian Tsui简历测试报告');
    console.log('='.repeat(60));
    
    const successfulTests = testResults.filter(r => r.success);
    const failedTests = testResults.filter(r => !r.success);
    
    console.log(`\n📊 总体结果: ${successfulTests.length}/${testResults.length} 函数测试通过`);
    console.log(`✅ 成功: ${successfulTests.length}`);
    console.log(`❌ 失败: ${failedTests.length}`);
    
    // 性能统计
    const totalDuration = testResults.reduce((sum, r) => sum + (r.duration || 0), 0);
    const avgDuration = totalDuration / testResults.length;
    const totalMemory = testResults.reduce((sum, r) => sum + (r.memUsage || 0), 0);
    const avgMemory = totalMemory / testResults.length;
    
    console.log(`\n⏱️  性能统计:`);
    console.log(`   总执行时间: ${totalDuration}ms`);
    console.log(`   平均执行时间: ${avgDuration.toFixed(2)}ms`);
    console.log(`   总内存使用: ${totalMemory}KB`);
    console.log(`   平均内存使用: ${avgMemory.toFixed(2)}KB`);
    
    // 详细结果
    console.log(`\n📝 详细结果:`);
    testResults.forEach((result, index) => {
        const status = result.success ? '✅' : '❌';
        console.log(`${status} 步骤${result.step}: ${result.function}`);
        
        if (result.success) {
            console.log(`   ⏱️  执行时间: ${result.duration}ms`);
            console.log(`   💾 内存使用: ${result.memUsage}KB`);
            console.log(`   🌐 网络时间: ${result.networkTime}ms`);
        } else {
            console.log(`   ❌ 错误: ${result.error || result.errorMsg}`);
        }
    });
    
    // 保存测试结果
    const reportPath = path.join(__dirname, 'test-results', `ian-tsui-test-${Date.now()}.json`);
    const reportDir = path.dirname(reportPath);
    
    if (!fs.existsSync(reportDir)) {
        fs.mkdirSync(reportDir, { recursive: true });
    }
    
    const testReport = {
        testId: testId,
        timestamp: new Date().toISOString(),
        subject: 'Ian Tsui Resume Analysis',
        resumeLength: resumeText.length,
        totalTests: testResults.length,
        successfulTests: successfulTests.length,
        failedTests: failedTests.length,
        performance: {
            totalDuration,
            avgDuration,
            totalMemory,
            avgMemory
        },
        results: testResults
    };
    
    fs.writeFileSync(reportPath, JSON.stringify(testReport, null, 2));
    console.log(`\n💾 测试报告已保存: ${reportPath}`);
    
    // 下一步建议
    if (successfulTests.length === testResults.length) {
        console.log('\n🎉 所有测试通过! 系统已准备好生产使用');
        console.log('\n📝 建议下一步:');
        console.log('1. 配置API网关，暴露HTTP接口');
        console.log('2. 设置函数间异步触发器');
        console.log('3. 配置日志监控和告警');
        console.log('4. 进行负载测试');
        console.log('5. 完善错误处理机制');
    } else {
        console.log('\n⚠️  部分测试失败，需要修复:');
        failedTests.forEach(test => {
            console.log(`   • ${test.function}: ${test.error || test.errorMsg}`);
        });
    }
    
    return testReport;
}

/**
 * 测试简历-JD匹配度分析
 */
async function testResumeJDMatch() {
    console.log('\n' + '='.repeat(60));
    console.log('🎯 测试简历-JD匹配度分析');
    console.log('='.repeat(60));
    
    const resumeText = resumeToText(IAN_TSUI_RESUME);
    const jdText = `${SAMPLE_JD.title}\n${SAMPLE_JD.location}\n${SAMPLE_JD.department}\n职位ID：${SAMPLE_JD.jobId}\n\n${SAMPLE_JD.description}`;
    
    console.log(`\n📊 匹配分析场景:`);
    console.log(`👤 候选人: Ian Tsui - Amazon客户经理 (6年电商运营经验)`);
    console.log(`🎯 目标职位: 智能客服产品运营专家 (需要NLP/算法/智能客服经验)`);
    console.log(`📋 预期结果: 低匹配度 - 技能领域不匹配`);
    
    const matchResult = await invokeFunction('jd-analyzer', {
        jdText: jdText,
        resumeText: resumeText,
        analysisType: 'match_analysis',
        userId: 'ian-tsui-test',
        includeMatchScore: true,
        detailedAnalysis: true
    });
    
    console.log(`\n📊 匹配度分析结果:`);
    if (matchResult.success && matchResult.result) {
        console.log(`✅ 分析成功`);
        
        // 尝试解析返回结果
        let analysisData = matchResult.result;
        if (typeof analysisData === 'string') {
            try {
                analysisData = JSON.parse(analysisData);
            } catch (e) {
                console.log(`📄 原始响应: ${analysisData}`);
            }
        }
        
        if (analysisData && typeof analysisData === 'object') {
            console.log(`\n🔍 分析详情:`);
            
            if (analysisData.matchScore !== undefined) {
                console.log(`   📊 匹配分数: ${analysisData.matchScore}%`);
            }
            
            if (analysisData.skills) {
                console.log(`   🎯 技能匹配:`);
                console.log(`      候选人技能: ${JSON.stringify(analysisData.candidateSkills || '未识别')}`);
                console.log(`      职位要求: ${JSON.stringify(analysisData.requiredSkills || '未识别')}`);
                console.log(`      匹配技能: ${JSON.stringify(analysisData.matchedSkills || '未识别')}`);
                console.log(`      缺失技能: ${JSON.stringify(analysisData.missingSkills || '未识别')}`);
            }
            
            if (analysisData.experienceMatch) {
                console.log(`   💼 经验匹配: ${analysisData.experienceMatch}`);
            }
            
            if (analysisData.recommendations) {
                console.log(`   💡 建议: ${JSON.stringify(analysisData.recommendations)}`);
            }
            
            console.log(`\n📊 完整分析数据:`);
            console.log(JSON.stringify(analysisData, null, 2));
        } else {
            console.log(`📄 分析数据: ${JSON.stringify(analysisData, null, 2)}`);
        }
        
        // 准确性评估
        console.log(`\n🔍 准确性评估:`);
        console.log(`❓ AI是否正确识别了技能不匹配?`);
        console.log(`❓ AI是否注意到电商经验与智能客服要求的差异?`);
        console.log(`❓ AI是否正确识别了候选人缺乏NLP/算法经验?`);
        
    } else {
        console.log(`❌ 分析失败: ${matchResult.error || matchResult.errorMsg}`);
    }
    
    return matchResult;
}

/**
 * 测试JD分析功能
 */
async function testJDAnalysis() {
    console.log('\n' + '='.repeat(50));
    console.log('🔍 测试JD分析功能');
    console.log('='.repeat(50));
    
    const jdText = `${SAMPLE_JD.title}\n${SAMPLE_JD.location}\n${SAMPLE_JD.department}\n${SAMPLE_JD.description}`;
    
    const jdResult = await invokeFunction('jd-analyzer', {
        jdText: jdText,
        analysisType: 'comprehensive',
        userId: 'ian-tsui-test'
    });
    
    console.log(`📋 JD分析结果:`);
    if (jdResult.success && jdResult.result) {
        console.log(`✅ 分析成功`);
        console.log(`📊 分析数据:`, JSON.stringify(jdResult.result, null, 2));
    } else {
        console.log(`❌ 分析失败: ${jdResult.error || jdResult.errorMsg}`);
    }
    
    return jdResult;
}

// 主程序
async function main() {
    console.log('🎯 Ian Tsui简历 - SCF函数集成测试 (含JD匹配分析)');
    console.log('=' * 70);
    console.log(`⏰ 开始时间: ${new Date().toISOString()}`);
    
    try {
        // 1. 测试完整简历分析流程
        const resumeTestReport = await testCompleteResumeAnalysis();
        
        // 2. 测试简历-JD匹配度分析 (新增)
        const matchTestResult = await testResumeJDMatch();
        
        // 3. 测试JD分析功能
        const jdTestResult = await testJDAnalysis();
        
        // 4. 生成综合报告
        console.log('\n' + '='.repeat(70));
        console.log('🎉 测试完成! 包含JD匹配度分析');
        console.log('='.repeat(70));
        console.log(`⏰ 结束时间: ${new Date().toISOString()}`);
        
        // 总结分析准确性
        console.log('\n📊 AI分析准确性评估总结:');
        console.log('1. 简历解析: 检查是否正确提取Ian的电商客户管理经验');
        console.log('2. JD理解: 检查是否正确识别智能客服职位的NLP/算法要求');
        console.log('3. 匹配度评估: 检查是否正确识别出技能不匹配');
        console.log('4. 建议质量: 检查是否提供了合理的改进建议');
        
        return {
            resumeTest: resumeTestReport,
            matchTest: matchTestResult,
            jdTest: jdTestResult
        };
        
    } catch (error) {
        console.error('❌ 测试过程中发生错误:', error);
        throw error;
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    main().catch(console.error);
}

module.exports = {
    testCompleteResumeAnalysis,
    testResumeJDMatch,
    testJDAnalysis,
    invokeFunction,
    IAN_TSUI_RESUME,
    SAMPLE_JD
}; 