#!/usr/bin/env node

/**
 * 构建和部署脚本 - 包含依赖管理
 * 为每个函数创建构建目录，安装依赖，打包并部署到腾讯云SCF
 */

const fs = require('fs');
const path = require('path');
const { execSync, spawn } = require('child_process');
const tencentcloud = require('tencentcloud-sdk-nodejs');

// 导入对应产品模块
const ScfClient = tencentcloud.scf.v20180416.Client;

// 腾讯云认证信息
const clientConfig = {
    credential: {
        secretId: process.env.TENCENT_SECRET_ID || 'YOUR_SECRET_ID',
        secretKey: process.env.TENCENT_SECRET_KEY || 'YOUR_SECRET_KEY'
    },
    region: 'ap-guangzhou',
    profile: {
        httpProfile: {
            endpoint: 'scf.tencentcloudapi.com'
        }
    }
};

const client = new ScfClient(clientConfig);

// 确保build目录存在
const buildDir = path.join(__dirname, 'build');
if (!fs.existsSync(buildDir)) {
    fs.mkdirSync(buildDir, { recursive: true });
}

/**
 * 清理构建目录
 */
function cleanBuildDir() {
    console.log('🧹 清理构建目录...');
    if (fs.existsSync(buildDir)) {
        fs.rmSync(buildDir, { recursive: true, force: true });
    }
    fs.mkdirSync(buildDir, { recursive: true });
}

/**
 * 复制文件到目标目录
 */
function copyFile(src, dest) {
    const destDir = path.dirname(dest);
    if (!fs.existsSync(destDir)) {
        fs.mkdirSync(destDir, { recursive: true });
    }
    fs.copyFileSync(src, dest);
}

/**
 * 复制目录
 */
function copyDirectory(src, dest) {
    if (!fs.existsSync(dest)) {
        fs.mkdirSync(dest, { recursive: true });
    }

    const entries = fs.readdirSync(src);

    for (const entry of entries) {
        const srcPath = path.join(src, entry);
        const destPath = path.join(dest, entry);

        if (fs.statSync(srcPath).isDirectory()) {
            copyDirectory(srcPath, destPath);
        } else {
            copyFile(srcPath, destPath);
        }
    }
}

/**
 * 为函数构建包含依赖的目录
 */
async function buildFunction(functionName, functionPath) {
    console.log(`\n🔧 构建函数: ${functionName}`);
    console.log(`📁 源路径: ${functionPath}`);

    const funcBuildDir = path.join(buildDir, functionName);

    try {
        // 创建函数构建目录
        if (fs.existsSync(funcBuildDir)) {
            fs.rmSync(funcBuildDir, { recursive: true, force: true });
        }
        fs.mkdirSync(funcBuildDir, { recursive: true });

        // 复制函数完整目录（包括子目录、辅助脚本、fonts 等），忽略 node_modules（后续 npm install 会重新安装）
        const copyFilter = entry => !entry.includes('node_modules');

        function copyDirRecursively(srcDir, destDir) {
            const entries = fs.readdirSync(srcDir);
            for (const entry of entries) {
                if (entry === 'node_modules') continue; // 跳过依赖目录
                const src = path.join(srcDir, entry);
                const dest = path.join(destDir, entry);
                if (fs.statSync(src).isDirectory()) {
                    fs.mkdirSync(dest, { recursive: true });
                    copyDirRecursively(src, dest);
                } else {
                    copyFile(src, dest);
                }
            }
        }

        copyDirRecursively(functionPath, funcBuildDir);
        console.log('  ✅ 复制函数代码目录');

        // 复制package.json
        const packageFile = path.join(functionPath, 'package.json');
        if (fs.existsSync(packageFile)) {
            copyFile(packageFile, path.join(funcBuildDir, 'package.json'));
            console.log(`  ✅ 复制配置文件: package.json`);

            // 安装生产依赖
            console.log(`  📦 安装生产依赖...`);
            try {
                execSync('npm install --production --no-package-lock', {
                    cwd: funcBuildDir,
                    stdio: ['pipe', 'pipe', 'pipe'],
                    encoding: 'utf8'
                });
                console.log(`  ✅ 依赖安装完成`);
            } catch (error) {
                console.warn(`  ⚠️  npm install 警告: ${error.message}`);
                // 继续执行，有些依赖可能是可选的
            }
        } else {
            console.log(`  ℹ️  没有package.json文件，跳过依赖安装`);
        }

        // 复制共享文件（如果存在）
        const sharedPath = path.join(process.cwd(), 'shared');
        if (fs.existsSync(sharedPath)) {
            const sharedDestPath = path.join(funcBuildDir, 'shared');
            copyDirectory(sharedPath, sharedDestPath);
            console.log(`  ✅ 复制共享文件目录`);
        }

        console.log(`  ✅ 函数 ${functionName} 构建完成`);
        return funcBuildDir;

    } catch (error) {
        console.error(`  ❌ 构建函数 ${functionName} 失败:`, error.message);
        throw error;
    }
}

/**
 * 将构建目录打包为ZIP
 */
async function packageFunction(functionName, buildPath) {
    console.log(`📦 打包函数: ${functionName}`);

    try {
        const JSZip = require('jszip');
        const zip = new JSZip();

        // 递归添加文件到ZIP
        function addToZip(dirPath, zipPath = '') {
            const entries = fs.readdirSync(dirPath);

            for (const entry of entries) {
                const fullPath = path.join(dirPath, entry);
                const zipFullPath = zipPath ? path.join(zipPath, entry) : entry;

                if (fs.statSync(fullPath).isDirectory()) {
                    addToZip(fullPath, zipFullPath);
                } else {
                    const content = fs.readFileSync(fullPath);
                    zip.file(zipFullPath.replace(/\\/g, '/'), content);
                }
            }
        }

        addToZip(buildPath);

        // 生成ZIP buffer
        const zipBuffer = await zip.generateAsync({
            type: 'nodebuffer',
            compression: 'DEFLATE',
            compressionOptions: { level: 9 }
        });

        console.log(`  ✅ 打包完成，大小: ${Math.round(zipBuffer.length / 1024)} KB`);
        return zipBuffer;

    } catch (error) {
        console.error(`  ❌ 打包函数 ${functionName} 失败:`, error.message);
        throw error;
    }
}

/**
 * 部署函数到腾讯云SCF
 */
async function deployFunction(functionName, zipBuffer, config = {}) {
    console.log(`🚀 部署函数: ${functionName}`);

    try {
        const zipBase64 = zipBuffer.toString('base64');
        console.log(`  📤 上传代码包: ${Math.round(zipBuffer.length / 1024)} KB`);

        const defaultConfig = {
            Runtime: 'Nodejs18.15',
            Handler: 'index.main_handler',
            MemorySize: 512,
            Timeout: 900,
            Environment: {
                Variables: [
                    { Key: 'NODE_ENV', Value: 'production' },
                    { Key: 'TENCENT_SECRET_ID', Value: clientConfig.credential.secretId },
                    { Key: 'TENCENT_SECRET_KEY', Value: clientConfig.credential.secretKey },
                    { Key: 'REGION', Value: 'ap-guangzhou' },
                    // 微信小程序配置
                    { Key: 'WECHAT_APPID', Value: 'wx2309e473610ea429' },
                    { Key: 'WECHAT_SECRET', Value: 'f8c2e8c1d4a3f6e9b2c7d5a8f1e4c9b6' },
                    // CloudBase配置
                    { Key: 'CLOUDBASE_ENV_ID', Value: 'prod-6gcfy0fq6022306f' },
                    { Key: 'CLOUDBASE_SECRET_ID', Value: clientConfig.credential.secretId },
                    { Key: 'CLOUDBASE_SECRET_KEY', Value: clientConfig.credential.secretKey },
                    // AI服务配置
                    { Key: 'OPENROUTER_API_KEY', Value: 'YOUR_OPENROUTER_API_KEY' }
                ]
            }
        };

        const finalConfig = { ...defaultConfig, ...config };

        try {
            // 尝试创建新函数
            console.log(`  ✨ 创建新函数...`);

            const createParams = {
                FunctionName: functionName,
                Code: {
                    ZipFile: zipBase64
                },
                Runtime: finalConfig.Runtime,
                Handler: finalConfig.Handler,
                MemorySize: finalConfig.MemorySize,
                Timeout: finalConfig.Timeout,
                Environment: finalConfig.Environment,
                Description: finalConfig.Description || `AI简历分析系统 - ${functionName}`
            };

            const createRes = await client.CreateFunction(createParams);
            console.log(`  ✅ 函数创建成功: ${createRes.FunctionName}`);

        } catch (error) {
            if (error.code === 'ResourceInUse.Function' || error.code === 'ResourceConflict.FunctionName') {
                // 函数已存在，更新函数代码
                console.log(`  📝 函数已存在，更新代码...`);

                const updateCodeParams = {
                    FunctionName: functionName,
                    ZipFile: zipBase64
                };

                await client.UpdateFunctionCode(updateCodeParams);
                console.log(`  ✅ 函数代码更新成功`);

                // 等待代码更新完成后再更新配置
                console.log(`  ⏳ 等待函数状态稳定...`);
                await new Promise(resolve => setTimeout(resolve, 5000));

                // 更新函数配置
                const updateConfigParams = {
                    FunctionName: functionName,
                    MemorySize: finalConfig.MemorySize,
                    Timeout: finalConfig.Timeout,
                    Environment: finalConfig.Environment,
                    Description: finalConfig.Description || `AI简历分析系统 - ${functionName}`
                };

                try {
                    await client.UpdateFunctionConfiguration(updateConfigParams);
                    console.log(`  ✅ 函数配置更新成功`);
                } catch (configError) {
                    if (configError.code === 'FailedOperation.UpdateFunctionConfiguration') {
                        console.log(`  ⚠️  配置更新跳过（函数仍在更新中）`);
                    } else {
                        throw configError;
                    }
                }
            } else {
                throw error;
            }
        }

        console.log(`  ✅ 函数 ${functionName} 部署成功`);
        return true;

    } catch (error) {
        console.error(`  ❌ 部署函数 ${functionName} 失败:`, error.message);
        if (error.code) {
            console.error(`     错误代码: ${error.code}`);
        }
        return false;
    }
}

/**
 * 主要的构建和部署流程
 */
async function main() {
    console.log('🚀 AI简历分析系统 - 构建和部署');
    console.log('==========================================');

    // 函数配置
    const functions = [
        {
            name: 'resume-gateway',
            path: '../cloudfunctions/resume-gateway',
            config: {
                Description: '简历解析网关 - 快速响应，异步处理',
                MemorySize: 256,
                Timeout: 30
            }
        },
        {
            name: 'task-dispatcher',
            path: '../cloudfunctions/task-dispatcher',
            config: {
                Description: '任务调度器 - 分发处理任务',
                MemorySize: 256,
                Timeout: 45
            }
        },
        {
            name: 'chunk-processor',
            path: '../cloudfunctions/chunk-processor',
            config: {
                Description: '简历分片处理器 - AI分析核心',
                MemorySize: 512,
                Timeout: 120
            }
        },
        {
            name: 'result-aggregator',
            path: '../cloudfunctions/result-aggregator',
            config: {
                Description: '结果聚合器 - 汇总分析结果',
                MemorySize: 256,
                Timeout: 30
            }
        },
        {
            name: 'resume-generate',
            path: '../cloudfunctions/resume-generate',
            config: {
                Description: '简历生成器 - 生成优化简历',
                MemorySize: 512,
                Timeout: 60
            }
        }
    ];

    // 清理构建目录
    cleanBuildDir();

    let successCount = 0;
    const results = [];

    for (const func of functions) {
        try {
            const functionPath = path.resolve(__dirname, func.path);

            if (!fs.existsSync(functionPath)) {
                console.log(`⚠️  跳过不存在的函数: ${functionPath}`);
                results.push({ name: func.name, status: 'skipped', reason: 'path not found' });
                continue;
            }

            // 构建函数
            const buildPath = await buildFunction(func.name, functionPath);

            // 打包函数
            const zipBuffer = await packageFunction(func.name, buildPath);

            // 部署函数
            const success = await deployFunction(func.name, zipBuffer, func.config);

            if (success) {
                successCount++;
                results.push({ name: func.name, status: 'success' });
                console.log(`✅ ${func.name} - 完成`);
            } else {
                results.push({ name: func.name, status: 'failed' });
                console.log(`❌ ${func.name} - 失败`);
            }

            // 添加延迟避免API限制
            await new Promise(resolve => setTimeout(resolve, 3000));

        } catch (error) {
            console.error(`❌ 处理函数 ${func.name} 时出错:`, error.message);
            results.push({ name: func.name, status: 'error', error: error.message });
        }
    }

    // 输出结果摘要
    console.log('\n==========================================');
    console.log('📊 部署结果摘要');
    console.log('==========================================');

    results.forEach(result => {
        const icon = result.status === 'success' ? '✅' :
            result.status === 'failed' ? '❌' :
                result.status === 'skipped' ? '⏭️' : '❌';
        console.log(`${icon} ${result.name}: ${result.status}`);
        if (result.reason) console.log(`   原因: ${result.reason}`);
        if (result.error) console.log(`   错误: ${result.error}`);
    });

    console.log(`\n🎉 部署完成! 成功: ${successCount}/${functions.length}`);

    if (successCount > 0) {
        console.log('\n📋 访问信息:');
        console.log(`🌍 地域: ap-guangzhou`);
        console.log(`🖥️  控制台: https://console.cloud.tencent.com/scf/list?rid=1&ns=default`);

        console.log('\n📝 下一步:');
        console.log('1. 测试函数功能');
        console.log('2. 配置API网关');
        console.log('3. 设置触发器');
        console.log('4. 监控函数运行状态');
    }

    // 清理构建目录
    console.log('\n🧹 清理临时文件...');
    if (fs.existsSync(buildDir)) {
        fs.rmSync(buildDir, { recursive: true, force: true });
    }

    console.log('✅ 构建和部署完成!');
}

// 检查并安装必要依赖
function checkDependencies() {
    const requiredPackages = ['jszip', 'tencentcloud-sdk-nodejs'];

    for (const pkg of requiredPackages) {
        try {
            require(pkg);
        } catch (error) {
            console.log(`📦 安装必要依赖: ${pkg}...`);
            try {
                execSync(`npm install ${pkg}`, { stdio: 'inherit' });
            } catch (installError) {
                console.error(`❌ 安装依赖 ${pkg} 失败:`, installError.message);
                process.exit(1);
            }
        }
    }
}

// 运行主程序
if (require.main === module) {
    checkDependencies();
    main().catch(error => {
        console.error('❌ 部署过程中发生错误:', error);
        process.exit(1);
    });
}

module.exports = { buildFunction, packageFunction, deployFunction };
