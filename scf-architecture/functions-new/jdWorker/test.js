// 本地测试脚本 for jdWorker

const assert = require('assert');
const { main } = require('./index.js');

// --- 配置环境变量 ---
// 在运行此脚本前，请务必替换成您的真实密钥
// 建议：使用 dotenv 从 .env 文件加载，以避免密钥泄露
process.env.CLOUDBASE_ENV_ID = "prod-6gcfy0fq6022306f"; // 替换
process.env.CLOUDBASE_SECRET_ID = "YOUR_SECRET_ID"; // 替换
process.env.CLOUDBASE_SECRET_KEY = "YOUR_SECRET_KEY"; // 替换
process.env.OPENROUTER_API_KEY = "YOUR_OPENROUTER_API_KEY"; // 替换

// --- 模拟 CMQ 事件 ---
const mockEvent = {
  Records: [
    {
      CMQ: {
        msgBody: JSON.stringify({
          type: 'JD_PROCESS',
          payload: {
            taskId: 'jd-test-task-001', // 模拟一个任务ID
            data: {
              jdText: `
                职位名称：高级前端工程师
                公司：未来科技
                职责：
                1. 负责核心产品的前端架构设计和开发。
                2. 使用 React 和 Vue.js 构建高性能、可复用的组件。
                3. 优化前端性能，提升用户体验。
                要求：
                1. 计算机科学或相关专业本科及以上学历。
                2. 3年以上前端开发经验，精通 JavaScript, HTML, CSS。
                3. 熟练掌握 React, Vue.js 等主流框架。
                4. 熟悉 Webpack, Node.js。
              `
            }
          }
        })
      }
    }
  ]
};

// --- 运行测试 ---
async function runTest() {
  console.log('--- 开始测试 jdWorker ---');
  try {
    const result = await main(mockEvent, {});

    console.log('--- 测试成功 ---');
    console.log('函数返回结果:', JSON.stringify(result, null, 2));

    // 简单断言
    assert.strictEqual(result.Records[0].success, true, '处理应返回 success: true');
    assert.ok(result.Records[0].result.extractedSkillsCount > 0, '应提取出至少一个技能');

    console.log('✅ 断言通过！');

  } catch (error) {
    console.error('--- 测试失败 ---');
    console.error('错误信息:', error.message);
    console.error('错误堆栈:', error.stack);
    process.exit(1); // 以错误码退出
  }
}

runTest(); 