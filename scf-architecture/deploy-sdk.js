#!/usr/bin/env node

/**
 * 使用腾讯云官方SDK部署SCF函数
 * 避免Serverless Framework的扫码问题
 */

const fs = require('fs');
const path = require('path');
const tencentcloud = require('tencentcloud-sdk-nodejs');

// 导入对应产品模块
const ScfClient = tencentcloud.scf.v20180416.Client;

// 腾讯云认证信息
const clientConfig = {
    credential: {
        secretId: 'YOUR_SECRET_ID',
        secretKey: 'YOUR_SECRET_KEY'
    },
    region: 'ap-guangzhou',
    profile: {
        httpProfile: {
            endpoint: 'scf.tencentcloudapi.com'
        }
    }
};

const client = new ScfClient(clientConfig);

/**
 * 打包函数代码为Base64
 */
function packageFunction(functionPath) {
    try {
        const JSZip = require('jszip');
        const zip = new JSZip();
        
        // 读取函数文件
        const functionFile = path.join(functionPath, 'index.js');
        if (fs.existsSync(functionFile)) {
            const content = fs.readFileSync(functionFile, 'utf8');
            zip.file('index.js', content);
            console.log(`  📄 添加文件: index.js (${content.length} 字符)`);
        } else {
            throw new Error(`函数文件不存在: ${functionFile}`);
        }
        
        // 检查package.json
        const packageFile = path.join(functionPath, 'package.json');
        if (fs.existsSync(packageFile)) {
            const content = fs.readFileSync(packageFile, 'utf8');
            zip.file('package.json', content);
            console.log(`  📄 添加文件: package.json`);
        }
        
        // 读取shared目录中的文件
        const sharedPath = path.join(process.cwd(), 'shared');
        if (fs.existsSync(sharedPath)) {
            const sharedFiles = fs.readdirSync(sharedPath);
            sharedFiles.forEach(file => {
                if (file.endsWith('.js')) {
                    const content = fs.readFileSync(path.join(sharedPath, file), 'utf8');
                    zip.file(`shared/${file}`, content);
                    console.log(`  📄 添加共享文件: shared/${file}`);
                }
            });
        }
        
        // 生成zip buffer
        return zip.generateAsync({ type: 'nodebuffer', compression: 'DEFLATE' });
        
    } catch (error) {
        console.error('打包函数代码失败:', error);
        throw error;
    }
}

/**
 * 创建或更新云函数
 */
async function deployFunction(functionName, functionPath, config = {}) {
    console.log(`\n🚀 部署函数: ${functionName}`);
    console.log(`📁 函数路径: ${functionPath}`);
    
    try {
        // 打包函数代码
        const zipBuffer = await packageFunction(functionPath);
        const zipBase64 = zipBuffer.toString('base64');
        console.log(`📦 代码包大小: ${Math.round(zipBuffer.length / 1024)} KB`);
        
        const defaultConfig = {
            Runtime: 'Nodejs18.15',
            Handler: 'index.main_handler',
            MemorySize: 512,
            Timeout: 900,
            Environment: {
                Variables: [
                    { Key: 'NODE_ENV', Value: 'production' },
                    { Key: 'TENCENT_SECRET_ID', Value: 'YOUR_SECRET_ID' },
                    { Key: 'TENCENT_SECRET_KEY', Value: 'YOUR_SECRET_KEY' },
                    { Key: 'REGION', Value: 'ap-guangzhou' },
                    // 微信小程序配置
                    { Key: 'WECHAT_APPID', Value: 'wx2309e473610ea429' },
                    { Key: 'WECHAT_SECRET', Value: 'f8c2e8c1d4a3f6e9b2c7d5a8f1e4c9b6' },
                    // CloudBase配置
                    { Key: 'CLOUDBASE_ENV_ID', Value: 'prod-6gcfy0fq6022306f' },
                    { Key: 'CLOUDBASE_SECRET_ID', Value: 'YOUR_SECRET_ID' },
                    { Key: 'CLOUDBASE_SECRET_KEY', Value: 'YOUR_SECRET_KEY' },
                    // AI服务配置
                    { Key: 'OPENROUTER_API_KEY', Value: 'YOUR_OPENROUTER_API_KEY' }
                ]
            }
        };
        
        const finalConfig = { ...defaultConfig, ...config };
        
        try {
            // 直接尝试创建新函数
            console.log(`✨ 创建新函数: ${functionName}`);
            
            const createParams = {
                FunctionName: functionName,
                Code: {
                    ZipFile: zipBase64
                },
                Runtime: finalConfig.Runtime,
                Handler: finalConfig.Handler,
                MemorySize: finalConfig.MemorySize,
                Timeout: finalConfig.Timeout,
                Environment: finalConfig.Environment,
                Description: finalConfig.Description || `简历分析系统 - ${functionName}`
            };
            
            const createRes = await client.CreateFunction(createParams);
            console.log(`✅ 函数创建成功: ${createRes.FunctionName}`);
            
        } catch (error) {
            if (error.code === 'ResourceInUse.Function' || error.code === 'ResourceConflict.FunctionName') {
                // 函数已存在，更新函数代码
                console.log(`📝 函数已存在，更新函数: ${functionName}`);
                
                const updateCodeParams = {
                    FunctionName: functionName,
                    ZipFile: zipBase64
                };
                
                await client.UpdateFunctionCode(updateCodeParams);
                console.log(`✅ 函数代码更新成功`);
                
                // 等待代码更新完成后再更新配置
                console.log(`⏳ 等待函数状态稳定...`);
                await new Promise(resolve => setTimeout(resolve, 5000));
                
                // 更新函数配置
                const updateConfigParams = {
                    FunctionName: functionName,
                    MemorySize: finalConfig.MemorySize,
                    Timeout: finalConfig.Timeout,
                    Environment: finalConfig.Environment,
                    Description: finalConfig.Description || `简历分析系统 - ${functionName}`
                };
                
                try {
                    await client.UpdateFunctionConfiguration(updateConfigParams);
                    console.log(`✅ 函数配置更新成功`);
                } catch (configError) {
                    if (configError.code === 'FailedOperation.UpdateFunctionConfiguration') {
                        console.log(`⚠️  配置更新跳过（函数仍在更新中），代码已更新`);
                    } else {
                        throw configError;
                    }
                }
            } else {
                throw error;
            }
        }
        
        console.log(`✅ 函数 ${functionName} 部署成功`);
        return true;
        
    } catch (error) {
        console.error(`❌ 部署函数 ${functionName} 失败:`, error.message);
        if (error.code) {
            console.error(`   错误代码: ${error.code}`);
        }
        return false;
    }
}

/**
 * 主部署流程
 */
async function main() {
    console.log('🚀 开始使用腾讯云SDK部署SCF函数...');
    console.log('========================================');
    
    const functions = [
        {
            name: 'resume-gateway',
            path: './functions/resume-gateway',
            config: {
                Description: '简历分析系统网关',
                MemorySize: 128,
                Timeout: 30
            }
        },
        {
            name: 'task-dispatcher',
            path: './functions/task-dispatcher',
            config: {
                Description: '任务分发器',
                MemorySize: 256,
                Timeout: 45
            }
        },
        {
            name: 'slice-worker',
            path: './functions/slice-worker',
            config: {
                Description: '核心AI处理器',
                MemorySize: 512,
                Timeout: 60
            }
        },
        {
            name: 'get-task-progress',
            path: './functions/get-task-progress',
            config: {
                Description: '进度查询',
                MemorySize: 128,
                Timeout: 15
            }
        },
        {
            name: 'result-aggregator',
            path: './functions/result-aggregator',
            config: {
                Description: '结果聚合器',
                MemorySize: 256,
                Timeout: 30
            }
        }
    ];
    
    let successCount = 0;
    
    for (const func of functions) {
        try {
            if (!fs.existsSync(func.path)) {
                console.log(`⚠️  跳过不存在的函数: ${func.path}`);
                continue;
            }
            
            const success = await deployFunction(func.name, func.path, func.config);
            
            if (success) {
                successCount++;
            }
            
            // 添加延迟避免API限制
            await new Promise(resolve => setTimeout(resolve, 2000));
            
        } catch (error) {
            console.error(`❌ 处理函数 ${func.name} 时出错:`, error);
        }
    }
    
    console.log('\n========================================');
    console.log(`🎉 部署完成! 成功: ${successCount}/${functions.filter(f => fs.existsSync(f.path)).length}`);
    
    if (successCount > 0) {
        console.log('✅ 函数部署成功！');
        
        // 输出访问信息
        console.log('\n📋 访问信息:');
        console.log(`地域: ap-guangzhou`);
        console.log(`控制台: https://console.cloud.tencent.com/scf/list?rid=1&ns=default`);
        console.log('函数列表:');
        functions.forEach(func => {
            if (fs.existsSync(func.path)) {
                console.log(`  - ${func.name}: https://console.cloud.tencent.com/scf/detail/1/default/${func.name}`);
            }
        });
        
        console.log('\n📝 下一步:');
        console.log('1. 访问腾讯云控制台查看函数状态');
        console.log('2. 配置API网关或触发器');
        console.log('3. 设置CloudBase数据库连接');
        console.log('4. 配置CMQ消息队列');
        
    } else {
        console.log('⚠️  没有函数部署成功，请检查错误日志');
    }
}

// 检查依赖
const requiredPackages = ['jszip'];
for (const pkg of requiredPackages) {
    try {
        require(pkg);
    } catch (error) {
        console.log(`📦 安装必要依赖: ${pkg}...`);
        require('child_process').execSync(`npm install ${pkg}`, { stdio: 'inherit' });
    }
}

// 运行主程序
if (require.main === module) {
    main().catch(console.error);
}

module.exports = { deployFunction, packageFunction }; 