const tencentcloud = require("tencentcloud-sdk-nodejs");

// 腾讯云SCF客户端配置
const ScfClient = tencentcloud.scf.v20180416.Client;

const clientConfig = {
    credential: {
        secretId: process.env.TENCENT_SECRET_ID || 'YOUR_SECRET_ID',
        secretKey: process.env.TENCENT_SECRET_KEY || 'YOUR_SECRET_KEY',
    },
    region: "ap-guangzhou",
    profile: {
        httpProfile: {
            endpoint: "scf.tencentcloudapi.com",
        },
    },
};

/**
 * 调用SCF函数进行测试
 */
async function invokeFunction(functionName, payload = {}) {
    const client = new ScfClient(clientConfig);
    
    try {
        const params = {
            FunctionName: functionName,
            InvocationType: "RequestResponse", // 同步调用
            Qualifier: "$LATEST",
            ClientContext: JSON.stringify(payload)
        };
        
        console.log(`🔄 调用函数: ${functionName}`);
        console.log(`📤 请求参数:`, JSON.stringify(payload, null, 2));
        
        const result = await client.Invoke(params);
        
        let response;
        try {
            response = JSON.parse(result.Result.RetMsg);
        } catch (e) {
            response = result.Result.RetMsg;
        }
        
        console.log(`✅ 调用成功!`);
        console.log(`📥 响应结果:`, JSON.stringify(response, null, 2));
        console.log(`⏱️  执行时间: ${result.Result.Duration}ms`);
        console.log(`💾 内存使用: ${result.Result.MemUsage}KB`);
        
        if (result.Result.ErrMsg) {
            console.log(`⚠️  错误信息: ${result.Result.ErrMsg}`);
        }
        
        // 检查是否是真正的成功
        const isSuccess = !result.Result.ErrMsg && 
                         (!response.errorCode || response.errorCode === 0) &&
                         (!response.statusCode || response.statusCode === 200);
        
        return {
            success: isSuccess,
            result: response,
            duration: result.Result.Duration,
            memUsage: result.Result.MemUsage,
            errorMsg: result.Result.ErrMsg
        };
        
    } catch (error) {
        console.error(`❌ 调用函数 ${functionName} 失败:`, error.message);
        return {
            success: false,
            error: error.message
        };
    }
}

/**
 * 测试所有函数
 */
async function testAllFunctions() {
    console.log('🚀 开始测试所有SCF函数...');
    console.log('========================================');
    
    const tests = [
        {
            name: 'resume-gateway',
            payload: {
                httpMethod: 'POST',
                path: '/resume/analyze',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    resumeText: "张三，软件工程师，3年Python开发经验",
                    userId: "test-user-001"
                })
            }
        },
        {
            name: 'task-dispatcher',
            payload: {
                taskId: "task-001",
                resumeContent: "前端开发工程师，熟悉React、Vue等框架",
                chunkSize: 500
            }
        },
        {
            name: 'chunk-processor',
            payload: {
                taskId: "task-001",
                sliceId: "slice-001",
                content: "5年工作经验，精通JavaScript和TypeScript"
            }
        },
        {
            name: 'result-aggregator',
            payload: {
                taskId: "task-001",
                results: [
                    { sliceId: "slice-001", analysis: "技术技能：JavaScript, TypeScript" },
                    { sliceId: "slice-002", analysis: "工作经验：5年前端开发" }
                ]
            }
        },
        {
            name: 'resume-generate',
            payload: {
                taskId: "task-001",
                analysisResults: {
                    skills: ["JavaScript", "TypeScript", "React"],
                    experience: "5年前端开发经验",
                    recommendations: ["增强后端技能", "学习云计算"]
                }
            }
        }
    ];
    
    const results = [];
    
    for (const test of tests) {
        console.log(`\n${'='.repeat(50)}`);
        const result = await invokeFunction(test.name, test.payload);
        results.push({
            functionName: test.name,
            ...result
        });
        
        // 添加延迟避免API限制
        await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    console.log('\n========================================');
    console.log('🎉 测试完成! 结果汇总:');
    console.log('========================================');
    
    results.forEach((result, index) => {
        const status = result.success ? '✅' : '❌';
        console.log(`${status} ${result.functionName}: ${result.success ? 'PASS' : 'FAIL'}`);
        if (result.success) {
            console.log(`   ⏱️  执行时间: ${result.duration}ms`);
            console.log(`   💾 内存使用: ${result.memUsage}KB`);
        } else {
            console.log(`   ❌ 错误: ${result.error || result.errorMsg}`);
            if (result.result && result.result.errorMessage) {
                console.log(`   📝 详细错误: ${result.result.errorMessage}`);
            }
        }
    });
    
    const successCount = results.filter(r => r.success).length;
    console.log(`\n📊 总体结果: ${successCount}/${results.length} 函数测试通过`);
    
    if (successCount === results.length) {
        console.log('🎉 所有函数都正常工作！');
        console.log('\n📝 建议下一步:');
        console.log('1. 配置API网关或应用集成');
        console.log('2. 设置函数间的触发器');
        console.log('3. 配置日志监控');
        console.log('4. 进行负载测试');
    } else {
        console.log('⚠️  部分函数需要修复，常见问题和解决方案:');
        console.log('\n🔧 故障排查建议:');
        console.log('1. "handler not found" 错误:');
        console.log('   - 检查函数入口点是否为 index.main_handler');
        console.log('   - 确认 index.js 文件存在并导出 main_handler 函数');
        console.log('2. "Cannot find module" 错误:');
        console.log('   - 重新运行构建脚本确保依赖正确安装');
        console.log('   - 检查 package.json 中的依赖版本');
        console.log('3. 函数逻辑错误:');
        console.log('   - 检查函数代码中的参数处理');
        console.log('   - 添加适当的错误处理和日志');
        
        // 提供具体的修复建议
        console.log('\n🛠️  快速修复命令:');
        console.log('```bash');
        console.log('# 重新构建和部署所有函数');
        console.log('node build-and-deploy.js');
        console.log('');
        console.log('# 检查特定函数的日志');
        console.log('# 访问: https://console.cloud.tencent.com/scf/list?rid=1&ns=default');
        console.log('```');
    }
    
    return results;
}

/**
 * 测试单个函数
 */
async function testSingleFunction(functionName, customPayload) {
    console.log(`🚀 测试单个函数: ${functionName}`);
    console.log('========================================');
    
    const payload = customPayload || {
        test: true,
        timestamp: Date.now(),
        message: "这是一个测试调用"
    };
    
    const result = await invokeFunction(functionName, payload);
    
    if (result.success) {
        console.log('🎉 函数测试成功！');
        console.log('\n📝 建议下一步:');
        console.log('1. 测试更复杂的输入数据');
        console.log('2. 检查函数的错误处理能力');
        console.log('3. 监控函数的性能指标');
    } else {
        console.log('❌ 函数测试失败！');
        console.log('\n🔧 建议排查:');
        console.log('1. 检查函数配置');
        console.log('2. 查看函数日志');
        console.log('3. 验证函数依赖');
    }
    
    return result;
}

/**
 * 健康检查 - 简单测试所有函数是否可访问
 */
async function healthCheck() {
    console.log('🏥 执行函数健康检查...');
    console.log('========================================');
    
    const functions = ['resume-gateway', 'task-dispatcher', 'chunk-processor', 'result-aggregator', 'resume-generate'];
    const results = [];
    
    for (const funcName of functions) {
        console.log(`\n🔍 检查函数: ${funcName}`);
        const result = await invokeFunction(funcName, { healthCheck: true });
        results.push({
            functionName: funcName,
            isAccessible: !!result,
            hasError: !result.success
        });
    }
    
    console.log('\n========================================');
    console.log('📊 健康检查结果:');
    console.log('========================================');
    
    results.forEach(result => {
        const status = result.isAccessible ? '🟢' : '🔴';
        const error = result.hasError ? ' (有错误)' : '';
        console.log(`${status} ${result.functionName}${error}`);
    });
    
    const healthyCount = results.filter(r => r.isAccessible && !r.hasError).length;
    console.log(`\n📊 健康状态: ${healthyCount}/${results.length} 函数正常`);
    
    return results;
}

// 命令行参数处理
if (require.main === module) {
    const args = process.argv.slice(2);
    
    if (args.length === 0) {
        // 测试所有函数
        testAllFunctions().catch(console.error);
    } else if (args[0] === 'health') {
        // 健康检查
        healthCheck().catch(console.error);
    } else if (args.length === 1) {
        // 测试单个函数
        testSingleFunction(args[0]).catch(console.error);
    } else if (args.length === 2) {
        // 测试单个函数并传入自定义payload
        try {
            const customPayload = JSON.parse(args[1]);
            testSingleFunction(args[0], customPayload).catch(console.error);
        } catch (e) {
            console.error('❌ 自定义payload格式错误，请提供有效的JSON字符串');
        }
    } else {
        console.log('📖 使用说明:');
        console.log('========================================');
        console.log('  测试所有函数: node test-functions.js');
        console.log('  健康检查:     node test-functions.js health');
        console.log('  测试单个函数: node test-functions.js <functionName>');
        console.log('  自定义测试:   node test-functions.js <functionName> \'{"key":"value"}\'');
        console.log('');
        console.log('📝 可用函数名:');
        console.log('  - resume-gateway');
        console.log('  - task-dispatcher');
        console.log('  - chunk-processor');
        console.log('  - result-aggregator');
        console.log('  - resume-generate');
    }
}

module.exports = { testAllFunctions, testSingleFunction, invokeFunction, healthCheck }; 