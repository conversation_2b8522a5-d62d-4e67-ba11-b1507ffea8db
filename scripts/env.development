# ========================================
# 开发环境变量配置
# 本文件包含开发环境的实际配置值
# ========================================

# 腾讯云配置
TENCENT_SECRET_ID=YOUR_SECRET_ID
TENCENT_SECRET_KEY=YOUR_SECRET_KEY
TENCENT_REGION=ap-beijing

# 腾讯云 SCF 配置
SCF_REGION=ap-beijing
SCF_RUNTIME=Nodejs18.15

# 腾讯云 CMQ 配置
CMQ_REGION=ap-beijing
CMQ_ENDPOINT=https://cmq-queue-bj.tencentcloudapi.com

# 腾讯云 COS 配置
COS_REGION=ap-beijing
COS_BUCKET=ai-resume-dev
COS_DOMAIN=https://ai-resume-dev.cos.ap-beijing.myqcloud.com

# 腾讯云 MySQL 配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=Aa123456
DB_NAME=ai_resume_dev

# OpenRouter API 配置
OPENROUTER_API_KEY=YOUR_OPENROUTER_API_KEY
OPENROUTER_BASE_URL=https://openrouter.ai/api/v1

# 微信小程序配置
WECHAT_APPID=wx2309e473610ea429
WECHAT_SECRET=

# 系统配置
SYSTEM_PASSWORD=Aa123456
NODE_ENV=development
LOG_LEVEL=debug

# 任务配置
TASK_TIMEOUT=300000
QUEUE_BATCH_SIZE=10
RETRY_ATTEMPTS=3

# 性能配置
MEMORY_SIZE=512
TIMEOUT=30

# 调试配置
DEBUG=true
VERBOSE_LOGGING=true 