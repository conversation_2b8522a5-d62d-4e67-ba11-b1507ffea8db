# ========================================
# 生产环境变量配置
# 本文件包含生产环境的实际配置值
# ========================================

# 腾讯云配置
TENCENT_SECRET_ID=YOUR_SECRET_ID
TENCENT_SECRET_KEY=YOUR_SECRET_KEY
TENCENT_REGION=ap-beijing

# 腾讯云 SCF 配置
SCF_REGION=ap-beijing
SCF_RUNTIME=Nodejs18.15

# 腾讯云 CMQ 配置
CMQ_REGION=ap-beijing
CMQ_ENDPOINT=https://cmq-queue-bj.tencentcloudapi.com

# 腾讯云 COS 配置
COS_REGION=ap-beijing
COS_BUCKET=ai-resume-prod
COS_DOMAIN=https://ai-resume-prod.cos.ap-beijing.myqcloud.com

# 腾讯云 MySQL 配置
DB_HOST=your-production-db-host
DB_PORT=3306
DB_USER=your-production-db-user
DB_PASSWORD=your-production-db-password
DB_NAME=ai_resume_prod

# OpenRouter API 配置
OPENROUTER_API_KEY=YOUR_OPENROUTER_API_KEY
OPENROUTER_BASE_URL=https://openrouter.ai/api/v1

# 微信小程序配置
WECHAT_APPID=wx2309e473610ea429
WECHAT_SECRET=

# 系统配置
SYSTEM_PASSWORD=Aa123456
NODE_ENV=production
LOG_LEVEL=info

# 任务配置
TASK_TIMEOUT=300000
QUEUE_BATCH_SIZE=20
RETRY_ATTEMPTS=3

# 性能配置
MEMORY_SIZE=1024
TIMEOUT=60

# 调试配置
DEBUG=false
VERBOSE_LOGGING=false 