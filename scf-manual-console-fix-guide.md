# SCF函数腾讯云控制台手动修复指南

## 🚨 紧急修复状态
**当前成功率**: 28.6% (2/7函数正常)  
**目标成功率**: ≥95%  
**修复方式**: 腾讯云控制台手动配置  

## 📊 函数状态分析

### ✅ 正常函数 (2/7)
1. **ai-resume-user-login**: HTTP 200 ✅
2. **cvGenerator**: HTTP 200 ✅

### ❌ 需要修复的函数 (5/7)
1. **ai-resume-main-api**: HTTP 401 (认证错误)
2. **ai-resume-token-verify**: HTTP 500 (内部错误)
3. **resumeWorker**: HTTP 443 (配置错误)
4. **gateway**: HTTP 443 (配置错误)
5. **jdWorker**: HTTP 443 (配置错误)

## 🔧 详细修复步骤

### 1. ai-resume-main-api (401错误修复)

**登录腾讯云控制台** → **云函数SCF** → **函数服务** → **ai-resume-main-api**

**步骤**:
1. 点击"函数配置"页签
2. 点击"编辑"按钮
3. 在"环境变量"部分添加以下配置:

```
USER_LOGIN_FUNCTION_URL = http://1341667342-l7dh36l4tt.ap-guangzhou.tencentscf.com
TOKEN_VERIFY_FUNCTION_URL = http://1341667342-ldcw844wvx.ap-guangzhou.tencentscf.com
WECHAT_APPID = wx2309e473610ea429
WECHAT_SECRET = 26ca850faf0fafcbad3cea9ce8ac9786
JWT_SECRET = ai-resume-jwt-secret-key-2024-wx2309e473610ea429
NODE_ENV = production
```

4. 点击"保存"

### 2. ai-resume-token-verify (500错误修复)

**路径**: 云函数SCF → ai-resume-token-verify → 函数配置

**环境变量配置**:
```
JWT_SECRET = ai-resume-jwt-secret-key-2024-wx2309e473610ea429
NODE_ENV = production
WECHAT_APPID = wx2309e473610ea429
```

### 3. resumeWorker (443错误修复)

**路径**: 云函数SCF → resumeWorker → 函数配置

**环境变量配置**:
```
NODE_ENV = production
CLOUDBASE_ENV_ID = cloud1-7gre7bf9deb695ed
OPENROUTER_API_KEY = YOUR_OPENROUTER_API_KEY
REGION = ap-guangzhou
```

**内存和超时配置**:
- 内存大小: 1024MB
- 超时时间: 60秒

### 4. gateway (443错误修复)

**路径**: 云函数SCF → gateway → 函数配置

**环境变量配置**:
```
NODE_ENV = production
REGION = ap-guangzhou
TENCENT_SECRET_ID = YOUR_SECRET_ID
TENCENT_SECRET_KEY = YOUR_SECRET_KEY
```

**内存和超时配置**:
- 内存大小: 256MB
- 超时时间: 30秒

### 5. jdWorker (443错误修复)

**路径**: 云函数SCF → jdWorker → 函数配置

**环境变量配置**:
```
NODE_ENV = production
CLOUDBASE_ENV_ID = cloud1-7gre7bf9deb695ed
CLOUDBASE_SECRET_ID = YOUR_SECRET_ID
CLOUDBASE_SECRET_KEY = YOUR_SECRET_KEY
OPENROUTER_API_KEY = YOUR_OPENROUTER_API_KEY
REGION = ap-guangzhou
```

**内存和超时配置**:
- 内存大小: 512MB
- 超时时间: 45秒

## 🔍 验证步骤

### 修复完成后验证
1. **等待配置生效**: 每个函数修复后等待2-3分钟
2. **测试函数URL**: 直接访问函数URL检查HTTP状态码
3. **运行验证脚本**: 在WeChat开发者工具中运行 `runFixVerification()`

### 预期结果
- **ai-resume-main-api**: HTTP 401 → HTTP 200/404
- **ai-resume-token-verify**: HTTP 500 → HTTP 200/404
- **resumeWorker**: HTTP 443 → HTTP 200/404
- **gateway**: HTTP 443 → HTTP 200/404
- **jdWorker**: HTTP 443 → HTTP 200/404

## 📊 成功率计算

**修复前**: 2/7 = 28.6%  
**修复后预期**: 7/7 = 100%  

## 🚨 关键注意事项

### 1. 环境变量格式
- 每个变量单独一行
- 格式: `变量名 = 变量值`
- 不要有多余的空格或引号

### 2. 保存顺序
- 先保存环境变量
- 再保存内存/超时配置
- 等待每次保存完成

### 3. 验证方法
- 使用浏览器直接访问函数URL
- 检查返回的HTTP状态码
- 200/404/405都表示连通性正常
- 401/443/500表示配置错误

## 🔗 相关链接

- **腾讯云控制台**: https://console.cloud.tencent.com/scf
- **CLS日志服务**: https://datasight-1341667342.clsconsole.tencent-cloud.com
- **函数URL列表**: 见 `utils/各种权限密码` 文件

## ⏰ 预计修复时间

- **单个函数修复**: 3-5分钟
- **全部函数修复**: 20-30分钟
- **配置生效等待**: 2-3分钟/函数
- **总计时间**: 约45分钟

## 🎯 修复优先级

1. **ai-resume-token-verify** (关键认证功能)
2. **ai-resume-main-api** (主API网关)
3. **gateway** (备用网关)
4. **jdWorker** (业务功能)
5. **resumeWorker** (业务功能)

---

**修复完成后，预期总体成功率将从28.6%提升到100%，满足≥95%的目标要求。**
