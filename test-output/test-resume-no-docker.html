<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>李明 - 简历</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Microsoft YaHei', 'PingFang SC', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #fff;
            padding: 40px;
            max-width: 800px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            border-bottom: 3px solid #2c3e50;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .name {
            font-size: 36px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #2c3e50;
        }
        .contact {
            color: #7f8c8d;
            font-size: 16px;
            display: flex;
            justify-content: center;
            gap: 20px;
            flex-wrap: wrap;
        }
        .section {
            margin: 30px 0;
        }
        .section-title {
            font-size: 22px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #2c3e50;
            border-left: 4px solid #3498db;
            padding-left: 12px;
        }
        .section-content {
            margin-left: 16px;
        }
        .work-item, .edu-item, .project-item {
            margin-bottom: 25px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #3498db;
        }
        .item-title {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 8px;
            font-size: 18px;
        }
        .item-company, .item-school {
            color: #3498db;
            margin-bottom: 5px;
            font-weight: 600;
        }
        .item-date {
            color: #7f8c8d;
            font-size: 14px;
            margin-bottom: 12px;
        }
        .item-description {
            line-height: 1.7;
        }
        .skills-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 10px;
            margin-top: 10px;
        }
        .skill-tag {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            padding: 8px 12px;
            border-radius: 20px;
            font-size: 14px;
            text-align: center;
            font-weight: 500;
        }
        .summary-text {
            background: #ecf0f1;
            padding: 20px;
            border-radius: 8px;
            font-style: italic;
            line-height: 1.8;
        }
        .achievements {
            margin-top: 10px;
        }
        .achievement-item {
            background: #e8f5e8;
            color: #27ae60;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            display: inline-block;
            margin: 2px 4px;
        }
        @media print {
            body { padding: 20px; }
            .work-item, .edu-item, .project-item { break-inside: avoid; }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="name">李明</div>
        <div class="contact">
            <span>📧 <EMAIL></span>
            <span>📱 13800138000</span>
            <span>📍 北京市海淀区中关村软件园</span>
            <span>🔗 https://github.com/liming</span>
        </div>
    </div>

    <div class="section">
        <div class="section-title">个人简介</div>
        <div class="section-content">
            <div class="summary-text">资深全栈开发工程师，拥有8年互联网产品开发经验。精通React、Vue.js、Node.js等前端技术栈，熟悉微服务架构和云原生技术。具备优秀的团队协作能力和项目管理经验，曾主导多个大型项目的架构设计和技术选型。</div>
        </div>
    </div>

    <div class="section">
        <div class="section-title">工作经历</div>
        <div class="section-content">
            
                <div class="work-item">
                    <div class="item-title">高级全栈工程师</div>
                    <div class="item-company">腾讯科技有限公司</div>
                    <div class="item-date">2021-03 - 至今</div>
                    <div class="item-description">负责微信生态相关产品的前后端开发工作，参与小程序云开发平台的架构设计。主要技术栈包括React、Node.js、云开发等。成功优化系统性能，用户响应时间提升40%。</div>
                </div>
            
                <div class="work-item">
                    <div class="item-title">前端技术专家</div>
                    <div class="item-company">阿里巴巴集团</div>
                    <div class="item-date">2019-06 - 2021-02</div>
                    <div class="item-description">负责淘宝前端基础设施建设，开发了多个内部工具和组件库。参与双11大促技术保障，确保系统稳定性。推动前端工程化建设，提升团队开发效率30%。</div>
                </div>
            
                <div class="work-item">
                    <div class="item-title">前端工程师</div>
                    <div class="item-company">字节跳动</div>
                    <div class="item-date">2017-08 - 2019-05</div>
                    <div class="item-description">参与今日头条和抖音等产品的前端开发工作。负责用户增长相关功能的开发和优化，通过A/B测试持续改进用户体验。掌握React、Vue.js等主流前端框架。</div>
                </div>
            
        </div>
    </div>

    <div class="section">
        <div class="section-title">教育背景</div>
        <div class="section-content">
            
                <div class="edu-item">
                    <div class="item-title">硕士 - 计算机科学与技术</div>
                    <div class="item-school">清华大学</div>
                    <div class="item-date">2015-09 - 2017-06</div>
                    <div>GPA: 3.8/4.0</div>
                    
                        <div class="achievements">
                            <span class="achievement-item">优秀毕业生</span><span class="achievement-item">国家奖学金</span><span class="achievement-item">ACM程序设计竞赛金奖</span>
                        </div>
                    
                </div>
            
                <div class="edu-item">
                    <div class="item-title">学士 - 软件工程</div>
                    <div class="item-school">北京理工大学</div>
                    <div class="item-date">2011-09 - 2015-06</div>
                    <div>GPA: 3.9/4.0</div>
                    
                        <div class="achievements">
                            <span class="achievement-item">专业第一名</span><span class="achievement-item">校级优秀学生干部</span>
                        </div>
                    
                </div>
            
        </div>
    </div>

    <div class="section">
        <div class="section-title">专业技能</div>
        <div class="section-content">
            <div class="skills-grid">
                <div class="skill-tag">JavaScript</div><div class="skill-tag">TypeScript</div><div class="skill-tag">React</div><div class="skill-tag">Vue.js</div><div class="skill-tag">Angular</div><div class="skill-tag">Node.js</div><div class="skill-tag">Express</div><div class="skill-tag">Koa</div><div class="skill-tag">Next.js</div><div class="skill-tag">Nuxt.js</div><div class="skill-tag">HTML5</div><div class="skill-tag">CSS3</div><div class="skill-tag">SCSS</div><div class="skill-tag">Less</div><div class="skill-tag">Tailwind CSS</div><div class="skill-tag">Webpack</div><div class="skill-tag">Vite</div><div class="skill-tag">Rollup</div><div class="skill-tag">Babel</div><div class="skill-tag">ESLint</div><div class="skill-tag">Git</div><div class="skill-tag">Docker</div><div class="skill-tag">Kubernetes</div><div class="skill-tag">AWS</div><div class="skill-tag">腾讯云</div><div class="skill-tag">MySQL</div><div class="skill-tag">MongoDB</div><div class="skill-tag">Redis</div><div class="skill-tag">PostgreSQL</div><div class="skill-tag">微信小程序</div><div class="skill-tag">云开发</div><div class="skill-tag">Serverless</div><div class="skill-tag">GraphQL</div>
            </div>
        </div>
    </div>

    
    <div class="section">
        <div class="section-title">项目经验</div>
        <div class="section-content">
            
                <div class="project-item">
                    <div class="item-title">企业级前端脚手架</div>
                    <div class="item-description">基于React和TypeScript的企业级前端开发脚手架，集成了完整的开发工具链和最佳实践。</div>
                    <div style="margin-top: 10px;">
                        <strong>技术栈:</strong> React, TypeScript, Webpack, Jest
                    </div>
                    
                        <div class="achievements">
                            <span class="achievement-item">团队采用率100%</span><span class="achievement-item">开发效率提升50%</span>
                        </div>
                    
                </div>
            
                <div class="project-item">
                    <div class="item-title">微服务监控平台</div>
                    <div class="item-description">基于Node.js和Vue.js的微服务监控平台，提供实时监控、告警和日志分析功能。</div>
                    <div style="margin-top: 10px;">
                        <strong>技术栈:</strong> Vue.js, Node.js, MongoDB, Docker
                    </div>
                    
                        <div class="achievements">
                            <span class="achievement-item">监控服务200+</span><span class="achievement-item">故障发现时间缩短80%</span>
                        </div>
                    
                </div>
            
        </div>
    </div>
    
</body>
</html>