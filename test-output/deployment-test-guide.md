
# 云函数部署后测试指导

## 1. 部署云托管服务

```bash
cd cloud-run/resume-snapshot
./deploy.sh
```

## 2. 更新云函数代码

通过云开发控制台或CLI更新 resumePreviewGenerator 函数代码。

## 3. 真实环境测试

### 在云开发控制台测试

1. 进入云开发控制台
2. 选择云函数 > resumePreviewGenerator
3. 使用以下测试数据:

```json
{
  "resumeData": {
    "personalInfo": {
      "name": "李明",
      "email": "<EMAIL>",
      "phone": "13800138000",
      "address": "北京市海淀区中关村软件园",
      "github": "https://github.com/liming",
      "linkedin": "https://linkedin.com/in/liming"
    },
    "summary": "资深全栈开发工程师，拥有8年互联网产品开发经验。精通React、Vue.js、Node.js等前端技术栈，熟悉微服务架构和云原生技术。具备优秀的团队协作能力和项目管理经验，曾主导多个大型项目的架构设计和技术选型。",
    "workExperience": [
      {
        "position": "高级全栈工程师",
        "company": "腾讯科技有限公司",
        "startDate": "2021-03",
        "endDate": "至今",
        "description": "负责微信生态相关产品的前后端开发工作，参与小程序云开发平台的架构设计。主要技术栈包括React、Node.js、云开发等。成功优化系统性能，用户响应时间提升40%。"
      },
      {
        "position": "前端技术专家",
        "company": "阿里巴巴集团",
        "startDate": "2019-06",
        "endDate": "2021-02",
        "description": "负责淘宝前端基础设施建设，开发了多个内部工具和组件库。参与双11大促技术保障，确保系统稳定性。推动前端工程化建设，提升团队开发效率30%。"
      }
    ],
    "education": [
      {
        "degree": "硕士",
        "major": "计算机科学与技术",
        "school": "清华大学",
        "startDate": "2015-09",
        "endDate": "2017-06",
        "gpa": "3.8/4.0",
        "achievements": [
          "优秀毕业生",
          "国家奖学金",
          "ACM程序设计竞赛金奖"
        ]
      }
    ],
    "skills": [
      "JavaScript",
      "TypeScript",
      "React",
      "Vue.js",
      "Angular",
      "Node.js",
      "Express",
      "Koa",
      "Next.js",
      "Nuxt.js",
      "HTML5",
      "CSS3",
      "SCSS",
      "Less",
      "Tailwind CSS",
      "Webpack",
      "Vite",
      "Rollup",
      "Babel",
      "ESLint",
      "Git",
      "Docker",
      "Kubernetes",
      "AWS",
      "腾讯云",
      "MySQL",
      "MongoDB",
      "Redis",
      "PostgreSQL",
      "微信小程序",
      "云开发",
      "Serverless",
      "GraphQL"
    ],
    "projects": [
      {
        "name": "企业级前端脚手架",
        "description": "基于React和TypeScript的企业级前端开发脚手架，集成了完整的开发工具链和最佳实践。",
        "technologies": [
          "React",
          "TypeScript",
          "Webpack",
          "Jest"
        ],
        "achievements": [
          "团队采用率100%",
          "开发效率提升50%"
        ]
      }
    ]
  },
  "format": "png"
}
```

### 预期结果

```json
{
  "statusCode": 200,
  "body": {
    "success": true,
    "data": {
      "png": {
        "imageUrl": "https://...",
        "fileID": "cloud://...",
        "cloudPath": "resume-previews/...",
        "fileSize": 245760
      },
      "sourceUrl": "https://...",
      "templateType": "professional",
      "generatedAt": "2025-07-28T...",
      "mode": "cloud-run-container",
      "service": "resume-snapshot"
    }
  }
}
```

## 4. 性能验证

- 响应时间应 < 10秒
- 成功率应 ≥ 95%
- PNG文件大小应在 200-500KB 范围内
- 图片尺寸应为 1240x1754 或更大

## 5. 故障排查

### 云函数日志
- 云开发控制台 > 云函数 > resumePreviewGenerator > 日志

### 云托管日志  
- 云托管控制台 > resume-snapshot > 日志

### 常见问题
1. 云托管服务无法访问 - 检查服务状态和网络配置
2. 截图生成失败 - 查看Puppeteer错误日志
3. 云存储上传失败 - 检查存储权限配置
