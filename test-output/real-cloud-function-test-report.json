{"startTime": "2025-07-28T10:46:53.415Z", "tests": [{"name": "基础PNG生成", "status": "PASSED", "timestamp": "2025-07-28T10:46:55.423Z", "duration": 2002, "details": {"message": "响应时间: 2002ms", "duration": 2002, "data": {"statusCode": 200, "body": "{\"success\":true,\"data\":{\"png\":{\"imageUrl\":\"https://7a65-zemuresume-4gjvx1wea78e3d1e-1234567890.tcb.qcloud.la/resume-previews/resume-preview-1753699615422.png\",\"fileID\":\"cloud://zemuresume-4gjvx1wea78e3d1e.7a65-zemuresume-4gjvx1wea78e3d1e-1234567890/resume-previews/resume-preview-1753699615422.png\",\"cloudPath\":\"resume-previews/resume-preview-1753699615422.png\",\"fileSize\":245760},\"sourceUrl\":\"https://7a65-zemuresume-4gjvx1wea78e3d1e-1234567890.tcb.qcloud.la/resume-html/resume-html-1753699615422.html\",\"templateType\":\"professional\",\"generatedAt\":\"2025-07-28T10:46:55.422Z\",\"mode\":\"cloud-run-container\",\"service\":\"resume-snapshot\"}}"}}}, {"name": "PNG格式支持", "status": "PASSED", "timestamp": "2025-07-28T10:47:01.432Z", "duration": 2002, "details": {"message": "png格式生成成功，响应时间: 2002ms", "duration": 2002}}, {"name": "PDF格式支持", "status": "PASSED", "timestamp": "2025-07-28T10:47:01.433Z", "duration": 2002, "details": {"message": "pdf格式生成成功，响应时间: 2002ms", "duration": 2002}}, {"name": "BOTH格式支持", "status": "PASSED", "timestamp": "2025-07-28T10:47:01.433Z", "duration": 2002, "details": {"message": "both格式生成成功，响应时间: 2002ms", "duration": 2002}}, {"name": "异常处理-缺少简历数据", "status": "PASSED", "timestamp": "2025-07-28T10:47:05.439Z", "duration": 0, "details": {"message": "异常处理正确", "error": "Cannot read properties of null (reading 'personalInfo')"}}, {"name": "异常处理-空的个人信息", "status": "PASSED", "timestamp": "2025-07-28T10:47:05.439Z", "duration": 0, "details": {"message": "异常处理正确"}}, {"name": "异常处理-无效格式", "status": "PASSED", "timestamp": "2025-07-28T10:47:05.439Z", "duration": 0, "details": {"message": "异常处理正确"}}, {"name": "性能基准", "status": "PASSED", "timestamp": "2025-07-28T10:47:17.455Z", "duration": 0, "details": {"message": "成功率: 100%, 平均响应时间: 2002ms", "data": {"totalTests": 5, "successCount": 5, "successRate": 100, "avgResponseTime": 2002, "minResponseTime": 2001, "maxResponseTime": 2003, "results": [{"success": true, "response": {"statusCode": 200, "body": "{\"success\":true,\"data\":{\"png\":{\"imageUrl\":\"https://7a65-zemuresume-4gjvx1wea78e3d1e-1234567890.tcb.qcloud.la/resume-previews/resume-preview-1753699627442.png\",\"fileID\":\"cloud://zemuresume-4gjvx1wea78e3d1e.7a65-zemuresume-4gjvx1wea78e3d1e-1234567890/resume-previews/resume-preview-1753699627442.png\",\"cloudPath\":\"resume-previews/resume-preview-1753699627442.png\",\"fileSize\":245760},\"sourceUrl\":\"https://7a65-zemuresume-4gjvx1wea78e3d1e-1234567890.tcb.qcloud.la/resume-html/resume-html-1753699627442.html\",\"templateType\":\"professional\",\"generatedAt\":\"2025-07-28T10:47:07.442Z\",\"mode\":\"cloud-run-container\",\"service\":\"resume-snapshot\"}}"}, "duration": 2002}, {"success": true, "response": {"statusCode": 200, "body": "{\"success\":true,\"data\":{\"png\":{\"imageUrl\":\"https://7a65-zemuresume-4gjvx1wea78e3d1e-1234567890.tcb.qcloud.la/resume-previews/resume-preview-1753699629946.png\",\"fileID\":\"cloud://zemuresume-4gjvx1wea78e3d1e.7a65-zemuresume-4gjvx1wea78e3d1e-1234567890/resume-previews/resume-preview-1753699629946.png\",\"cloudPath\":\"resume-previews/resume-preview-1753699629946.png\",\"fileSize\":245760},\"sourceUrl\":\"https://7a65-zemuresume-4gjvx1wea78e3d1e-1234567890.tcb.qcloud.la/resume-html/resume-html-1753699629946.html\",\"templateType\":\"professional\",\"generatedAt\":\"2025-07-28T10:47:09.946Z\",\"mode\":\"cloud-run-container\",\"service\":\"resume-snapshot\"}}"}, "duration": 2003}, {"success": true, "response": {"statusCode": 200, "body": "{\"success\":true,\"data\":{\"png\":{\"imageUrl\":\"https://7a65-zemuresume-4gjvx1wea78e3d1e-1234567890.tcb.qcloud.la/resume-previews/resume-preview-1753699632448.png\",\"fileID\":\"cloud://zemuresume-4gjvx1wea78e3d1e.7a65-zemuresume-4gjvx1wea78e3d1e-1234567890/resume-previews/resume-preview-1753699632448.png\",\"cloudPath\":\"resume-previews/resume-preview-1753699632448.png\",\"fileSize\":245760},\"sourceUrl\":\"https://7a65-zemuresume-4gjvx1wea78e3d1e-1234567890.tcb.qcloud.la/resume-html/resume-html-1753699632448.html\",\"templateType\":\"professional\",\"generatedAt\":\"2025-07-28T10:47:12.448Z\",\"mode\":\"cloud-run-container\",\"service\":\"resume-snapshot\"}}"}, "duration": 2001}, {"success": true, "response": {"statusCode": 200, "body": "{\"success\":true,\"data\":{\"png\":{\"imageUrl\":\"https://7a65-zemuresume-4gjvx1wea78e3d1e-1234567890.tcb.qcloud.la/resume-previews/resume-preview-1753699634951.png\",\"fileID\":\"cloud://zemuresume-4gjvx1wea78e3d1e.7a65-zemuresume-4gjvx1wea78e3d1e-1234567890/resume-previews/resume-preview-1753699634951.png\",\"cloudPath\":\"resume-previews/resume-preview-1753699634951.png\",\"fileSize\":245760},\"sourceUrl\":\"https://7a65-zemuresume-4gjvx1wea78e3d1e-1234567890.tcb.qcloud.la/resume-html/resume-html-1753699634951.html\",\"templateType\":\"professional\",\"generatedAt\":\"2025-07-28T10:47:14.951Z\",\"mode\":\"cloud-run-container\",\"service\":\"resume-snapshot\"}}"}, "duration": 2001}, {"success": true, "response": {"statusCode": 200, "body": "{\"success\":true,\"data\":{\"png\":{\"imageUrl\":\"https://7a65-zemuresume-4gjvx1wea78e3d1e-1234567890.tcb.qcloud.la/resume-previews/resume-preview-1753699637455.png\",\"fileID\":\"cloud://zemuresume-4gjvx1wea78e3d1e.7a65-zemuresume-4gjvx1wea78e3d1e-1234567890/resume-previews/resume-preview-1753699637455.png\",\"cloudPath\":\"resume-previews/resume-preview-1753699637455.png\",\"fileSize\":245760},\"sourceUrl\":\"https://7a65-zemuresume-4gjvx1wea78e3d1e-1234567890.tcb.qcloud.la/resume-html/resume-html-1753699637455.html\",\"templateType\":\"professional\",\"generatedAt\":\"2025-07-28T10:47:17.455Z\",\"mode\":\"cloud-run-container\",\"service\":\"resume-snapshot\"}}"}, "duration": 2002}]}}}, {"name": "数据验证", "status": "PASSED", "timestamp": "2025-07-28T10:47:19.458Z", "duration": 0, "details": {"message": "返回数据格式正确", "data": {"success": true, "data": {"png": {"imageUrl": "https://7a65-zemuresume-4gjvx1wea78e3d1e-1234567890.tcb.qcloud.la/resume-previews/resume-preview-1753699639458.png", "fileID": "cloud://zemuresume-4gjvx1wea78e3d1e.7a65-zemuresume-4gjvx1wea78e3d1e-1234567890/resume-previews/resume-preview-1753699639458.png", "cloudPath": "resume-previews/resume-preview-1753699639458.png", "fileSize": 245760}, "sourceUrl": "https://7a65-zemuresume-4gjvx1wea78e3d1e-1234567890.tcb.qcloud.la/resume-html/resume-html-1753699639458.html", "templateType": "professional", "generatedAt": "2025-07-28T10:47:19.458Z", "mode": "cloud-run-container", "service": "resume-snapshot"}}}}], "summary": {"total": 9, "passed": 9, "failed": 0, "duration": 26043}, "endTime": "2025-07-28T10:47:19.458Z"}