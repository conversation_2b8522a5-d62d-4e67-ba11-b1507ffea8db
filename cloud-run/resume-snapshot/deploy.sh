#!/bin/bash

# Resume Snapshot Service 部署脚本
# 用于构建和部署云托管容器服务

set -e

# 配置变量
REGISTRY="ccr.ccs.tencentyun.com"
NAMESPACE="your-namespace"  # 请替换为您的命名空间
SERVICE_NAME="resume-snapshot"
VERSION="latest"
IMAGE_TAG="${REGISTRY}/${NAMESPACE}/${SERVICE_NAME}:${VERSION}"

echo "🚀 开始部署 Resume Snapshot Service"
echo "📦 镜像标签: ${IMAGE_TAG}"

# 检查Docker是否运行
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker 未运行，请先启动 Docker"
    exit 1
fi

# 构建镜像
echo "🔨 构建Docker镜像..."
docker build -t ${IMAGE_TAG} .

if [ $? -eq 0 ]; then
    echo "✅ 镜像构建成功"
else
    echo "❌ 镜像构建失败"
    exit 1
fi

# 推送镜像
echo "📤 推送镜像到容器镜像仓库..."
docker push ${IMAGE_TAG}

if [ $? -eq 0 ]; then
    echo "✅ 镜像推送成功"
else
    echo "❌ 镜像推送失败"
    exit 1
fi

echo "🎉 部署完成！"
echo ""
echo "📋 下一步操作："
echo "1. 登录腾讯云云托管控制台"
echo "2. 创建或更新服务: ${SERVICE_NAME}"
echo "3. 选择镜像: ${IMAGE_TAG}"
echo "4. 配置服务参数:"
echo "   - 端口: 80"
echo "   - 最小实例: 0"
echo "   - 最大实例: 5"
echo "   - CPU: 1核"
echo "   - 内存: 2GB"
echo "5. 发布版本"
echo ""
echo "🔗 控制台地址: https://console.cloud.tencent.com/tcb/service"
