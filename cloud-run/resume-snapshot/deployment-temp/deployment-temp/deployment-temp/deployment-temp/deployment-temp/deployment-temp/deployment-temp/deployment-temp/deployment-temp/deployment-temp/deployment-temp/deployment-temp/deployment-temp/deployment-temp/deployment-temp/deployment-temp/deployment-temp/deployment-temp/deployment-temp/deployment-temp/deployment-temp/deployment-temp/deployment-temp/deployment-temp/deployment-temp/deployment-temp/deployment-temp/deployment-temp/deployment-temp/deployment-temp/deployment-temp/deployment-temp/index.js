const express = require('express');
const puppeteer = require('puppeteer');
const cors = require('cors');
const helmet = require('helmet');

const app = express();

// 安全中间件
app.use(helmet());
app.use(cors());
app.use(express.json({ limit: '10mb' }));

// 健康检查端点
app.get('/health', (req, res) => {
  res.json({ status: 'healthy', timestamp: new Date().toISOString() });
});

// 截图服务端点
app.post('/snapshot', async (req, res) => {
  console.log('📸 收到截图请求:', req.body);

  const { url, format = 'png', width = 1240, height = 1754, options = {} } = req.body;

  if (!url) {
    return res.status(400).json({
      error: 'url参数是必需的',
      code: 'MISSING_URL'
    });
  }

  let browser;
  try {
    // 启动浏览器
    browser = await puppeteer.launch({
      headless: 'new',
      executablePath: process.env.PUPPETEER_EXECUTABLE_PATH,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--no-first-run',
        '--no-zygote',
        '--disable-gpu',
        '--disable-web-security',
        '--disable-features=VizDisplayCompositor'
      ]
    });

    const page = await browser.newPage();

    // 设置视口
    await page.setViewport({
      width: parseInt(width),
      height: parseInt(height),
      deviceScaleFactor: options.deviceScaleFactor || 1
    });

    // 设置用户代理
    await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');

    console.log('🌐 正在访问URL:', url);

    // 访问页面
    await page.goto(url, {
      waitUntil: options.waitUntil || 'networkidle2',
      timeout: options.timeout || 30000
    });

    // 等待额外时间确保页面完全渲染
    if (options.delay) {
      await page.waitForTimeout(parseInt(options.delay));
    }

    let buffer;
    let contentType;

    if (format === 'pdf') {
      console.log('📄 生成PDF...');
      buffer = await page.pdf({
        format: options.pdfFormat || 'A4',
        printBackground: options.printBackground !== false,
        margin: options.margin || {
          top: '1cm',
          right: '1cm',
          bottom: '1cm',
          left: '1cm'
        }
      });
      contentType = 'application/pdf';
    } else {
      console.log('🖼️ 生成PNG截图...');
      buffer = await page.screenshot({
        type: 'png',
        fullPage: options.fullPage !== false,
        quality: options.quality || 90
      });
      contentType = 'image/png';
    }

    await browser.close();

    console.log(`✅ 成功生成${format.toUpperCase()}, 大小: ${buffer.length} bytes`);

    // 设置响应头
    res.set({
      'Content-Type': contentType,
      'Content-Length': buffer.length,
      'Cache-Control': 'no-cache'
    });

    res.send(buffer);

  } catch (error) {
    console.error('❌ 截图服务错误:', error);

    if (browser) {
      try {
        await browser.close();
      } catch (closeError) {
        console.error('❌ 关闭浏览器失败:', closeError);
      }
    }

    res.status(500).json({
      error: '截图生成失败',
      message: error.message,
      code: 'SNAPSHOT_ERROR'
    });
  }
});

// 错误处理中间件
app.use((error, req, res, next) => {
  console.error('🚨 服务器错误:', error);
  res.status(500).json({
    error: '内部服务器错误',
    message: error.message
  });
});

// 404 处理
app.use((req, res) => {
  res.status(404).json({
    error: '接口不存在',
    path: req.path
  });
});

const port = process.env.PORT || 80;

app.listen(port, () => {
  console.log(`🚀 Resume Snapshot Service 启动成功`);
  console.log(`📡 监听端口: ${port}`);
  console.log(`🔧 Puppeteer 可执行路径: ${process.env.PUPPETEER_EXECUTABLE_PATH}`);
  console.log(`⏰ 启动时间: ${new Date().toISOString()}`);
});

// 优雅关闭
process.on('SIGTERM', () => {
  console.log('📴 收到SIGTERM信号，正在优雅关闭...');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('📴 收到SIGINT信号，正在优雅关闭...');
  process.exit(0);
});
