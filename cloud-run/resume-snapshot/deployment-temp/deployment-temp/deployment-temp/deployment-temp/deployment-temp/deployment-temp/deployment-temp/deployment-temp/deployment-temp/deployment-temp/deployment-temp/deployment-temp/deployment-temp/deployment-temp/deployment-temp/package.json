{"name": "resume-snapshot", "version": "1.0.0", "description": "Resume snapshot service for generating PNG and PDF previews", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js"}, "dependencies": {"@cloudbase/node-sdk": "^3.10.1", "cors": "^2.8.5", "express": "^4.18.0", "helmet": "^7.0.0", "puppeteer": "^21.0.0"}, "devDependencies": {"nodemon": "^3.0.0"}, "engines": {"node": ">=18.0.0"}, "keywords": ["resume", "snapshot", "pdf", "png", "puppeteer", "cloudbase"], "author": "AI Resume System", "license": "MIT"}