FROM node:18-slim

WORKDIR /app

# 安装 Puppeteer 所需依赖
RUN apt-get update && apt-get install -y \
    chromium fonts-liberation libasound2 libatk-bridge2.0-0 \
    libdrm2 libxcomposite1 libxdamage1 libxrandr2 xdg-utils \
    --no-install-recommends && \
    rm -rf /var/lib/apt/lists/*

# 设置 Puppeteer 环境变量
ENV PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true \
    PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium

# 复制 package 文件并安装依赖
COPY package*.json ./
RUN npm install --only=production

# 复制应用代码
COPY . .

# 暴露端口
EXPOSE 80

# 启动应用
CMD ["node", "index.js"]
