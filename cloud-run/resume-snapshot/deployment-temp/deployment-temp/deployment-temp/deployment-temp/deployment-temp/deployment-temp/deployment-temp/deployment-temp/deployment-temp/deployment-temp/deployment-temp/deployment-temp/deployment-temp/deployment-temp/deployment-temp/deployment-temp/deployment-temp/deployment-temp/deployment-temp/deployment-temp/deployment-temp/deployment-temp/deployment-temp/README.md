# Resume Snapshot Service

基于 Puppeteer 的简历截图和PDF生成服务，部署在腾讯云云托管上。

## 功能特性

- 🖼️ 生成高质量PNG截图
- 📄 生成PDF文档
- 🚀 高性能容器化部署
- 🔒 安全的内网访问
- 📊 健康检查支持

## API 接口

### 健康检查
```
GET /health
```

### 生成截图/PDF
```
POST /snapshot
```

**请求参数:**
```json
{
  "url": "https://example.com/resume",
  "format": "png|pdf",
  "width": 1240,
  "height": 1754,
  "options": {
    "fullPage": true,
    "quality": 90,
    "delay": 1000,
    "waitUntil": "networkidle2",
    "timeout": 30000,
    "deviceScaleFactor": 1,
    "pdfFormat": "A4",
    "printBackground": true,
    "margin": {
      "top": "1cm",
      "right": "1cm", 
      "bottom": "1cm",
      "left": "1cm"
    }
  }
}
```

**响应:**
- 成功: 返回二进制图片/PDF数据
- 失败: 返回JSON错误信息

## 部署指南

### 1. 构建镜像
```bash
cd cloud-run/resume-snapshot
docker build -t ccr.ccs.tencentyun.com/<your-namespace>/resume-snapshot:latest .
```

### 2. 推送镜像
```bash
docker push ccr.ccs.tencentyun.com/<your-namespace>/resume-snapshot:latest
```

### 3. 云托管部署
1. 进入腾讯云云托管控制台
2. 创建新服务 `resume-snapshot`
3. 选择上述镜像
4. 配置:
   - 端口: 80
   - 最小实例: 0
   - 最大实例: 5
   - CPU: 1核
   - 内存: 2GB

### 4. 环境变量
- `PORT`: 服务端口 (默认80)
- `PUPPETEER_EXECUTABLE_PATH`: Chromium路径 (默认/usr/bin/chromium)

## 本地开发

```bash
npm install
npm run dev
```

测试接口:
```bash
curl -X POST http://localhost:80/snapshot \
  -H "Content-Type: application/json" \
  -d '{"url":"https://www.baidu.com","format":"png"}' \
  --output test.png
```

## 性能优化

- 使用轻量级 node:18-slim 基础镜像
- 预安装 Chromium 避免运行时下载
- 支持实例自动缩容到0
- 内网访问减少延迟

## 安全特性

- Helmet 安全头设置
- CORS 跨域控制
- 请求大小限制 (10MB)
- 沙箱模式运行

## 监控指标

- 健康检查: `/health`
- 响应时间监控
- 错误率统计
- 内存使用监控
