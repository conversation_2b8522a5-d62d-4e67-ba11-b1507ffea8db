/**
 * Resume Snapshot Service 测试脚本
 * 用于测试云托管服务的功能
 */

const fs = require('fs');
const path = require('path');

// 测试配置
const TEST_CONFIG = {
    // 本地测试
    local: {
        baseUrl: 'http://localhost:8080',
        enabled: true
    },
    // 云托管测试 (需要替换为实际的服务地址)
    cloudRun: {
        baseUrl: 'https://your-service-id-your-env.service.tcloudbase.com',
        enabled: false
    }
};

/**
 * 发送HTTP请求
 */
async function makeRequest(url, options = {}) {
    const fetch = (await import('node-fetch')).default;

    try {
        const response = await fetch(url, {
            timeout: 30000,
            ...options
        });

        return {
            ok: response.ok,
            status: response.status,
            statusText: response.statusText,
            headers: Object.fromEntries(response.headers.entries()),
            data: response.ok ? await response.buffer() : await response.text()
        };
    } catch (error) {
        return {
            ok: false,
            error: error.message
        };
    }
}

/**
 * 测试健康检查
 */
async function testHealthCheck(baseUrl) {
    console.log('🔍 测试健康检查...');

    const result = await makeRequest(`${baseUrl}/health`);

    if (result.ok) {
        const data = JSON.parse(result.data.toString());
        console.log('✅ 健康检查通过:', data);
        return true;
    } else {
        console.log('❌ 健康检查失败:', result.error || result.statusText);
        return false;
    }
}

/**
 * 测试截图功能
 */
async function testSnapshot(baseUrl, format = 'png') {
    console.log(`🖼️ 测试${format.toUpperCase()}截图功能...`);

    const testData = {
        url: 'https://www.baidu.com',
        format: format,
        width: 1240,
        height: 800,
        options: {
            fullPage: false,
            quality: 90,
            delay: 2000
        }
    };

    const result = await makeRequest(`${baseUrl}/snapshot`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(testData)
    });

    if (result.ok) {
        const outputDir = path.join(__dirname, 'test-output');
        if (!fs.existsSync(outputDir)) {
            fs.mkdirSync(outputDir);
        }

        const filename = `test-${format}-${Date.now()}.${format}`;
        const filepath = path.join(outputDir, filename);

        fs.writeFileSync(filepath, result.data);

        console.log(`✅ ${format.toUpperCase()}截图成功:`);
        console.log(`   文件大小: ${result.data.length} bytes`);
        console.log(`   保存路径: ${filepath}`);
        return true;
    } else {
        console.log(`❌ ${format.toUpperCase()}截图失败:`, result.error || result.statusText);
        if (result.data && typeof result.data === 'string') {
            try {
                const errorData = JSON.parse(result.data);
                console.log('   错误详情:', errorData);
            } catch (e) {
                console.log('   错误信息:', result.data);
            }
        }
        return false;
    }
}

/**
 * 测试简历HTML截图
 */
async function testResumeSnapshot(baseUrl) {
    console.log('📄 测试简历HTML截图...');

    // 创建测试HTML
    const testHtml = `
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>测试简历</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; line-height: 1.6; }
            .header { text-align: center; border-bottom: 2px solid #333; padding-bottom: 20px; margin-bottom: 30px; }
            .name { font-size: 24px; font-weight: bold; margin-bottom: 10px; }
            .contact { color: #666; }
            .section { margin: 20px 0; }
            .section-title { font-size: 18px; font-weight: bold; margin-bottom: 10px; color: #333; }
        </style>
    </head>
    <body>
        <div class="header">
            <div class="name">张三</div>
            <div class="contact">13800138000 | <EMAIL></div>
        </div>
        <div class="section">
            <div class="section-title">个人简介</div>
            <div>资深前端开发工程师，具有5年以上开发经验...</div>
        </div>
        <div class="section">
            <div class="section-title">工作经历</div>
            <div>
                <strong>高级前端工程师</strong> - 某科技公司<br>
                2020.01 - 至今<br>
                负责公司核心产品的前端开发工作...
            </div>
        </div>
    </body>
    </html>
    `;

    // 保存测试HTML
    const htmlPath = path.join(__dirname, 'test-output', 'test-resume.html');
    const outputDir = path.dirname(htmlPath);
    if (!fs.existsSync(outputDir)) {
        fs.mkdirSync(outputDir, { recursive: true });
    }
    fs.writeFileSync(htmlPath, testHtml);

    // 使用file:// URL进行测试
    const fileUrl = `file://${htmlPath}`;

    const testData = {
        url: fileUrl,
        format: 'png',
        width: 1240,
        height: 1754,
        options: {
            fullPage: true,
            quality: 90,
            delay: 1000,
            deviceScaleFactor: 2
        }
    };

    const result = await makeRequest(`${baseUrl}/snapshot`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(testData)
    });

    if (result.ok) {
        const filename = `test-resume-${Date.now()}.png`;
        const filepath = path.join(outputDir, filename);

        fs.writeFileSync(filepath, result.data);

        console.log('✅ 简历截图成功:');
        console.log(`   文件大小: ${result.data.length} bytes`);
        console.log(`   保存路径: ${filepath}`);
        return true;
    } else {
        console.log('❌ 简历截图失败:', result.error || result.statusText);
        return false;
    }
}

/**
 * 运行所有测试
 */
async function runTests() {
    console.log('🧪 开始测试 Resume Snapshot Service\n');

    for (const [envName, config] of Object.entries(TEST_CONFIG)) {
        if (!config.enabled) {
            console.log(`⏭️ 跳过 ${envName} 环境测试\n`);
            continue;
        }

        console.log(`🌍 测试环境: ${envName}`);
        console.log(`🔗 服务地址: ${config.baseUrl}\n`);

        const results = [];

        // 健康检查
        results.push(await testHealthCheck(config.baseUrl));

        // PNG截图测试
        results.push(await testSnapshot(config.baseUrl, 'png'));

        // PDF生成测试
        results.push(await testSnapshot(config.baseUrl, 'pdf'));

        // 简历截图测试
        results.push(await testResumeSnapshot(config.baseUrl));

        const successCount = results.filter(r => r).length;
        const totalCount = results.length;

        console.log(`\n📊 ${envName} 环境测试结果: ${successCount}/${totalCount} 通过\n`);

        if (successCount === totalCount) {
            console.log(`✅ ${envName} 环境所有测试通过！\n`);
        } else {
            console.log(`❌ ${envName} 环境部分测试失败\n`);
        }
    }

    console.log('🎉 测试完成！');
}

// 运行测试
if (require.main === module) {
    runTests().catch(console.error);
}

module.exports = {
    testHealthCheck,
    testSnapshot,
    testResumeSnapshot,
    runTests
};
