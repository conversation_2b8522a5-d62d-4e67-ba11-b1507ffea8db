#!/bin/bash

# 微信云托管部署包准备脚本
# 使用方法: chmod +x prepare-deployment.sh && ./prepare-deployment.sh

echo "🚀 开始准备微信云托管部署包..."

# 检查当前目录
if [ ! -f "package.json" ]; then
    echo "❌ 错误: 请在项目根目录下运行此脚本"
    exit 1
fi

# 创建临时目录
TEMP_DIR="deployment-temp"
PACKAGE_NAME="ai-resume-snapshot-$(date +%Y%m%d-%H%M%S).tar.gz"

echo "📦 创建临时目录: $TEMP_DIR"
rm -rf $TEMP_DIR
mkdir -p $TEMP_DIR

# 复制必要文件
echo "📋 复制项目文件..."
cp -r . $TEMP_DIR/

# 进入临时目录
cd $TEMP_DIR

# 清理不需要的文件
echo "🧹 清理不需要的文件..."
rm -rf node_modules
rm -rf .git
rm -rf .DS_Store
rm -rf *.log
rm -rf .env
rm -rf .env.local
rm -rf coverage
rm -rf .nyc_output
rm -rf dist
rm -rf build
rm -rf prepare-deployment.sh

# 显示将要打包的文件
echo "📁 将要打包的文件:"
find . -type f | head -20
echo "..."

# 创建压缩包
cd ..
echo "📦 创建压缩包: $PACKAGE_NAME"
tar -czf $PACKAGE_NAME -C $TEMP_DIR .

# 清理临时目录
rm -rf $TEMP_DIR

# 显示结果
echo "✅ 部署包准备完成!"
echo "📦 文件名: $PACKAGE_NAME"
echo "📏 文件大小: $(du -h $PACKAGE_NAME | cut -f1)"
echo ""
echo "🔗 下一步操作:"
echo "1. 访问: https://cloud.weixin.qq.com/cloudrun"
echo "2. 选择环境: prod"
echo "3. 创建服务: ai-resume-snapshot"
echo "4. 上传文件: $PACKAGE_NAME"
echo ""
echo "📋 详细部署指南请查看: 微信云托管手动部署指南.md"
