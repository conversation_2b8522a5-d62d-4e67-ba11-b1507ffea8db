# 微信云托管手动部署完整指南

## 📋 部署信息总览

### 基本信息
- **项目名称**: AI简历截图服务
- **服务名称**: `ai-resume-snapshot`
- **环境ID**: `prod-2gczdho212bd52ca`
- **微信AppID**: `wx4fa04593aaf3bb76`
- **项目类型**: 后台服务 (Node.js + Puppeteer)
- **容器端口**: 80

### 文件路径
- **项目目录**: `/Users/<USER>/Desktop/ai-resume/cloud-run/resume-snapshot`
- **Dockerfile**: `./Dockerfile`
- **配置文件**: `./wxcloud.config.js`

## 🚀 手动部署步骤

### 第1步：访问微信云托管控制台

1. **打开浏览器**，访问：https://cloud.weixin.qq.com/cloudrun
2. **登录账号**：使用微信扫码登录
3. **选择环境**：点击环境选择器，选择 `prod` 环境

### 第2步：创建新服务

1. **点击"新建服务"按钮**
2. **填写服务基本信息**：
   ```
   服务名称: ai-resume-snapshot
   服务描述: AI简历截图服务 - 将HTML简历转换为PNG预览图
   ```
3. **选择部署方式**：选择 "上传代码包"
4. **点击"下一步"**

### 第3步：配置服务参数

#### 3.1 基础配置
```
服务名称: ai-resume-snapshot
服务描述: AI简历截图服务 - 将HTML简历转换为PNG预览图
```

#### 3.2 容器配置
```
容器端口: 80
协议类型: HTTP
```

#### 3.3 资源配置
```
CPU: 1核 (推荐)
内存: 2GB (推荐，Puppeteer需要较多内存)
```

#### 3.4 实例配置
```
最小实例数: 0 (节省成本)
最大实例数: 10 (根据需求调整)
```

#### 3.5 网络配置
```
公网访问: 开启
自定义域名: 暂不配置 (可后续添加)
```

### 第4步：上传代码包

#### 4.1 准备代码包
在本地终端执行以下命令打包：
```bash
cd /Users/<USER>/Desktop/ai-resume/cloud-run/resume-snapshot
tar -czf ai-resume-snapshot.tar.gz --exclude=node_modules --exclude=.git --exclude=.DS_Store .
```

#### 4.2 上传文件
1. **点击"选择文件"按钮**
2. **选择刚才创建的压缩包**: `ai-resume-snapshot.tar.gz`
3. **等待上传完成**（显示上传进度条）

### 第5步：构建配置

#### 5.1 构建方式
```
构建方式: Dockerfile
Dockerfile路径: ./Dockerfile
```

#### 5.2 构建参数（保持默认）
```
构建超时时间: 1800秒 (30分钟)
构建资源: 标准 (2核4GB)
```

### 第6步：环境变量配置

**暂不需要配置环境变量**（当前项目无需特殊环境变量）

### 第7步：健康检查配置

```
健康检查路径: /health (如果应用支持)
或者保持默认: / 
检查间隔: 30秒
超时时间: 5秒
失败阈值: 3次
```

### 第8步：版本发布配置

#### 8.1 版本信息
```
版本名称: v1.0.0
版本描述: AI简历截图服务初始版本
```

#### 8.2 发布策略
```
发布策略: 全量发布
流量分配: 100% (新版本)
```

### 第9步：确认并部署

1. **检查所有配置信息**
2. **点击"确认部署"按钮**
3. **等待部署完成**（通常需要5-15分钟）

## 📊 详细配置参数表

### 服务配置
| 配置项 | 数值/选项 | 说明 |
|--------|-----------|------|
| 服务名称 | `ai-resume-snapshot` | 必须唯一，只能包含小写字母、数字、连字符 |
| 容器端口 | `80` | 应用监听端口 |
| CPU | `1核` | 推荐配置，可根据负载调整 |
| 内存 | `2GB` | Puppeteer需要较多内存 |
| 最小实例数 | `0` | 节省成本，按需启动 |
| 最大实例数 | `10` | 根据预期并发调整 |

### 网络配置
| 配置项 | 数值/选项 | 说明 |
|--------|-----------|------|
| 公网访问 | `开启` | 允许外部访问 |
| HTTPS | `自动` | 系统自动配置SSL证书 |
| 自定义域名 | `暂不配置` | 可后续在域名管理中添加 |

### 构建配置
| 配置项 | 数值/选项 | 说明 |
|--------|-----------|------|
| 构建方式 | `Dockerfile` | 使用项目中的Dockerfile |
| Dockerfile路径 | `./Dockerfile` | 相对于代码包根目录 |
| 构建超时 | `1800秒` | 30分钟，足够安装依赖 |

## 🔍 部署后验证

### 第1步：检查服务状态
1. **在服务列表中找到** `ai-resume-snapshot`
2. **查看状态**：应显示为 "运行中"
3. **查看实例数**：应显示至少1个实例

### 第2步：获取访问地址
1. **点击服务名称**进入详情页
2. **复制访问地址**：格式类似 `https://ai-resume-snapshot-prod-2gczdho212bd52ca.ap-shanghai.run.tcloudbase.com`

### 第3步：测试服务
使用以下curl命令测试：
```bash
curl -X POST https://ai-resume-snapshot-prod-2gczdho212bd52ca.ap-shanghai.run.tcloudbase.com/snapshot \
  -H "Content-Type: application/json" \
  -d '{
    "url": "https://example.com",
    "format": "png",
    "width": 1240,
    "height": 1754
  }'
```

## ⚠️ 注意事项

### 部署过程中可能遇到的问题

1. **构建失败**
   - 检查Dockerfile语法
   - 确认依赖包可以正常安装
   - 查看构建日志定位问题

2. **服务启动失败**
   - 检查端口配置是否正确
   - 确认应用能在容器中正常启动
   - 查看运行日志

3. **访问超时**
   - 检查健康检查配置
   - 确认应用响应时间
   - 调整超时参数

### 成本优化建议

1. **实例配置**：
   - 开发测试：1核2GB，最小实例0
   - 生产环境：2核4GB，最小实例1

2. **监控告警**：
   - 设置CPU、内存使用率告警
   - 配置请求量和错误率监控

## 🔗 相关链接

- **微信云托管控制台**: https://cloud.weixin.qq.com/cloudrun
- **服务详情页**: https://cloud.weixin.qq.com/cloudrun/service/detail?envId=prod-2gczdho212bd52ca&serviceName=ai-resume-snapshot
- **部署日志查看**: 在服务详情页的"版本管理"标签页中查看

## 📞 技术支持

如遇到部署问题，请联系：
- **微信云托管技术支持**: 通过控制台右下角的帮助按钮
- **文档中心**: https://developers.weixin.qq.com/miniprogram/dev/wxcloudservice/wxcloudrun/

---

**部署完成后，请更新云函数中的服务地址为实际的访问地址！**
