/**
 * 测试云托管服务URL格式
 */

const https = require('https');

const ENV_ID = 'zemuresume-4gjvx1wea78e3d1e';
const SERVICE_NAME = 'resume-snapshot';

// 可能的URL格式
const possibleUrls = [
    `https://${SERVICE_NAME}-${ENV_ID}.service.tcloudbase.com`,
    `https://${ENV_ID}-${SERVICE_NAME}.service.tcloudbase.com`,
    `https://${SERVICE_NAME}-${ENV_ID}.ap-shanghai.run.tcloudbase.com`,
    `https://${ENV_ID}-${SERVICE_NAME}.ap-shanghai.run.tcloudbase.com`,
    `https://${SERVICE_NAME}.${ENV_ID}.service.tcloudbase.com`,
    `https://${ENV_ID}.${SERVICE_NAME}.service.tcloudbase.com`,
    `https://${SERVICE_NAME}.${ENV_ID}.ap-shanghai.run.tcloudbase.com`,
    `https://${ENV_ID}.${SERVICE_NAME}.ap-shanghai.run.tcloudbase.com`
];

async function testUrl(url) {
    return new Promise((resolve) => {
        const healthUrl = `${url}/health`;
        console.log(`🔍 测试: ${healthUrl}`);
        
        const urlObj = new URL(healthUrl);
        const options = {
            hostname: urlObj.hostname,
            port: urlObj.port || 443,
            path: urlObj.pathname,
            method: 'GET',
            timeout: 5000
        };

        const req = https.request(options, (res) => {
            let data = '';
            res.on('data', (chunk) => {
                data += chunk;
            });
            
            res.on('end', () => {
                console.log(`   状态码: ${res.statusCode}`);
                console.log(`   响应: ${data}`);
                
                if (res.statusCode === 200) {
                    try {
                        const json = JSON.parse(data);
                        if (json.status === 'healthy') {
                            console.log(`✅ 找到正确的URL: ${url}`);
                            resolve({ success: true, url, response: json });
                            return;
                        }
                    } catch (e) {
                        // 忽略JSON解析错误
                    }
                }
                resolve({ success: false, url, statusCode: res.statusCode, response: data });
            });
        });

        req.on('error', (error) => {
            console.log(`   错误: ${error.message}`);
            resolve({ success: false, url, error: error.message });
        });

        req.on('timeout', () => {
            console.log(`   超时`);
            req.destroy();
            resolve({ success: false, url, error: 'timeout' });
        });

        req.end();
    });
}

async function findCorrectUrl() {
    console.log('🚀 开始测试云托管服务URL格式...\n');
    
    for (const url of possibleUrls) {
        const result = await testUrl(url);
        if (result.success) {
            console.log(`\n🎉 成功找到云托管服务URL: ${result.url}`);
            return result.url;
        }
        console.log(''); // 空行分隔
    }
    
    console.log('❌ 未找到可用的云托管服务URL');
    return null;
}

// 运行测试
if (require.main === module) {
    findCorrectUrl().then(url => {
        if (url) {
            console.log(`\n📋 使用此URL更新云函数代码:`);
            console.log(`const serviceUrl = '${url}/snapshot';`);
        }
    }).catch(console.error);
}

module.exports = { findCorrectUrl, testUrl };
