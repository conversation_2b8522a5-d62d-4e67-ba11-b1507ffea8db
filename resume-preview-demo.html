<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简历预览演示</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <style>
        .resume-container {
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }
        .resume-container:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
        }
        .loading {
            position: relative;
        }
        .loading:after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.8) url('https://i.imgur.com/JfPpwOA.gif') no-repeat center;
            background-size: 50px;
            z-index: 10;
        }
    </style>
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <header class="text-center mb-12">
            <h1 class="text-4xl font-bold text-gray-800 mb-2">简历预览演示</h1>
            <p class="text-gray-600">基于云函数的简历预览生成服务</p>
        </header>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
            <!-- 左侧：简历表单 -->
            <div class="bg-white p-6 rounded-lg shadow-md">
                <h2 class="text-2xl font-semibold mb-6 text-gray-700">简历信息</h2>
                <form id="resumeForm" class="space-y-4">
                    <!-- 个人信息 -->
                    <div>
                        <h3 class="text-lg font-medium text-gray-700 mb-3">个人信息</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">姓名</label>
                                <input type="text" id="name" name="name" value="张三" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">电话</label>
                                <input type="text" id="phone" name="phone" value="13800138000" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>
                            <div class="md:col-span-2">
                                <label class="block text-sm font-medium text-gray-700 mb-1">邮箱</label>
                                <input type="email" id="email" name="email" value="<EMAIL>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>
                        </div>
                    </div>

                    <!-- 个人简介 -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">个人简介</label>
                        <textarea id="summary" name="summary" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">具有5年前端开发经验的软件工程师，熟练掌握React、Vue等现代前端框架，有丰富的项目开发和团队协作经验。</textarea>
                    </div>

                    <!-- 工作经历 -->
                    <div>
                        <h3 class="text-lg font-medium text-gray-700 mb-3">工作经历</h3>
                        <div id="workExperience" class="space-y-4">
                            <div class="p-4 border border-gray-200 rounded-md">
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">职位</label>
                                        <input type="text" name="position" value="高级前端工程师" class="w-full px-3 py-2 border border-gray-300 rounded-md">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">公司</label>
                                        <input type="text" name="company" value="腾讯科技有限公司" class="w-full px-3 py-2 border border-gray-300 rounded-md">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">开始时间</label>
                                        <input type="text" name="startDate" value="2021-03" class="w-full px-3 py-2 border border-gray-300 rounded-md">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">结束时间</label>
                                        <input type="text" name="endDate" value="至今" class="w-full px-3 py-2 border border-gray-300 rounded-md">
                                    </div>
                                    <div class="md:col-span-2">
                                        <label class="block text-sm font-medium text-gray-700 mb-1">工作描述</label>
                                        <textarea name="description" rows="2" class="w-full px-3 py-2 border border-gray-300 rounded-md">负责微信小程序和Web应用的前端开发，参与多个千万级用户产品的开发和维护。</textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 教育背景 -->
                    <div>
                        <h3 class="text-lg font-medium text-gray-700 mb-3">教育背景</h3>
                        <div id="education" class="space-y-4">
                            <div class="p-4 border border-gray-200 rounded-md">
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">学校</label>
                                        <input type="text" name="school" value="清华大学" class="w-full px-3 py-2 border border-gray-300 rounded-md">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">学位</label>
                                        <input type="text" name="degree" value="本科" class="w-full px-3 py-2 border border-gray-300 rounded-md">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">专业</label>
                                        <input type="text" name="major" value="计算机科学与技术" class="w-full px-3 py-2 border border-gray-300 rounded-md">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">时间段</label>
                                        <div class="flex items-center space-x-2">
                                            <input type="text" name="eduStartDate" value="2015-09" class="w-full px-3 py-2 border border-gray-300 rounded-md">
                                            <span>-</span>
                                            <input type="text" name="eduEndDate" value="2019-06" class="w-full px-3 py-2 border border-gray-300 rounded-md">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 技能 -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">技能 (用逗号分隔)</label>
                        <input type="text" id="skills" name="skills" value="JavaScript, React, Vue.js, TypeScript, Node.js, Python, MySQL, MongoDB" class="w-full px-3 py-2 border border-gray-300 rounded-md">
                    </div>

                    <div class="pt-4">
                        <button type="submit" class="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors">
                            生成预览
                        </button>
                    </div>
                </form>
            </div>

            <!-- 右侧：预览结果 -->
            <div class="bg-white p-6 rounded-lg shadow-md">
                <h2 class="text-2xl font-semibold mb-6 text-gray-700">预览结果</h2>
                <div id="previewContainer" class="space-y-6">
                    <div id="htmlPreview" class="resume-container bg-white border border-gray-200 rounded-lg overflow-hidden">
                        <div class="p-4 bg-gray-50 border-b border-gray-200 flex justify-between items-center">
                            <h3 class="font-medium text-gray-700">HTML 预览</h3>
                            <a id="htmlLink" href="#" target="_blank" class="text-blue-600 hover:text-blue-800 text-sm">在新窗口打开</a>
                        </div>
                        <div class="p-4">
                            <iframe id="resumeFrame" class="w-full h-96 border-0" srcdoc="<p class='text-center text-gray-500 mt-10'>点击"生成预览"按钮查看结果</p>"></iframe>
                        </div>
                    </div>

                    <div id="pngPreview" class="resume-container bg-white border border-gray-200 rounded-lg overflow-hidden">
                        <div class="p-4 bg-gray-50 border-b border-gray-200 flex justify-between items-center">
                            <h3 class="font-medium text-gray-700">PNG 预览</h3>
                            <a id="pngLink" href="#" target="_blank" class="text-blue-600 hover:text-blue-800 text-sm">下载图片</a>
                        </div>
                        <div class="p-4 flex justify-center">
                            <img id="resumeImage" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII=" class="max-w-full h-auto" alt="简历预览">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const resumeForm = document.getElementById('resumeForm');
            const previewContainer = document.getElementById('previewContainer');
            const resumeFrame = document.getElementById('resumeFrame');
            const resumeImage = document.getElementById('resumeImage');
            const htmlLink = document.getElementById('htmlLink');
            const pngLink = document.getElementById('pngLink');

            resumeForm.addEventListener('submit', async function(e) {
                e.preventDefault();
                
                // 显示加载状态
                previewContainer.classList.add('loading');
                
                // 收集表单数据
                const formData = {
                    resumeData: {
                        personalInfo: {
                            name: document.getElementById('name').value,
                            phone: document.getElementById('phone').value,
                            email: document.getElementById('email').value
                        },
                        summary: document.getElementById('summary').value,
                        workExperience: [{
                            position: document.querySelector('[name="position"]').value,
                            company: document.querySelector('[name="company"]').value,
                            startDate: document.querySelector('[name="startDate"]').value,
                            endDate: document.querySelector('[name="endDate"]').value,
                            description: document.querySelector('[name="description"]').value
                        }],
                        education: [{
                            school: document.querySelector('[name="school"]').value,
                            degree: document.querySelector('[name="degree"]').value,
                            major: document.querySelector('[name="major"]').value,
                            startDate: document.querySelector('[name="eduStartDate"]').value,
                            endDate: document.querySelector('[name="eduEndDate"]').value
                        }],
                        skills: document.getElementById('skills').value.split(',').map(skill => skill.trim())
                    },
                    format: 'png'
                };

                try {
                    // 调用云函数
                    const response = await fetch('https://zemuresume-4gjvx1wea78e3d1e-1341667342.service.tcloudbase.com/resume-preview', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(formData)
                    });

                    if (!response.ok) {
                        throw new Error('网络请求失败');
                    }

                    const result = await response.json();
                    
                    if (result.success) {
                        // 更新HTML预览
                        resumeFrame.srcdoc = result.data.html.content;
                        htmlLink.href = result.data.html.url;
                        
                        // 更新PNG预览
                        resumeImage.src = result.data.png.imageUrl;
                        pngLink.href = result.data.png.imageUrl;
                    } else {
                        alert('生成预览失败: ' + result.error);
                    }
                } catch (error) {
                    console.error('预览生成错误:', error);
                    alert('预览生成错误: ' + error.message);
                } finally {
                    // 移除加载状态
                    previewContainer.classList.remove('loading');
                }
            });
        });
    </script>
</body>
</html>
