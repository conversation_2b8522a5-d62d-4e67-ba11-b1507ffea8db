# AI简历系统 - 微信云托管部署指南

## 项目概述

本项目包含两个核心服务：
1. **resumePreviewGenerator** - 云函数：负责生成简历HTML和调用截图服务
2. **resume-snapshot** - 微信云托管：负责将HTML转换为PNG/PDF预览图

## 架构说明

```
用户请求 → resumePreviewGenerator云函数 → 生成HTML → 上传到云存储 → 调用resume-snapshot服务 → 生成PNG预览 → 返回结果
```

## 部署步骤

### 1. 环境确认

- ✅ 云开发环境：`zemuresume-4gjvx1wea78e3d1e`
- ✅ 环境状态：正常运行
- ✅ 付费模式：预付费模式（建议升级到按量付费以支持微信云托管）

### 2. 微信云托管部署

#### 2.1 安装CLI工具
```bash
npm install -g @wxcloud/cli
```

#### 2.2 获取CLI密钥
1. 访问 [微信云托管控制台-设置-CLI密钥](https://cloud.weixin.qq.com/cloudrun/settings/other)
2. 生成新的CLI密钥（需要管理员扫码确认）
3. 保存密钥备用

#### 2.3 登录CLI
```bash
wxcloud login -a <微信AppID> -k <CLI密钥>
```

#### 2.4 创建服务
```bash
wxcloud service:create --envId zemuresume-4gjvx1wea78e3d1e --serviceName resume-snapshot --isPublic
```

#### 2.5 部署服务
在 `cloud-run/resume-snapshot` 目录下执行：
```bash
wxcloud run:deploy --envId zemuresume-4gjvx1wea78e3d1e --serviceName resume-snapshot --targetDir=. --dockerfile=Dockerfile --containerPort=80 --releaseType=FULL --remark="简历截图服务初始版本"
```

### 3. 云函数部署

#### 3.1 部署增强版云函数
```bash
# 进入云函数目录
cd cloudfunctions/resumePreviewGenerator

# 复制增强版文件
cp index-enhanced.js index.js
cp package-enhanced.json package.json

# 部署云函数
cloudbase functions:deploy resumePreviewGenerator
```

### 4. 服务地址配置

微信云托管服务部署成功后，服务地址格式为：
```
https://resume-snapshot-zemuresume-4gjvx1wea78e3d1e.ap-shanghai.run.tcloudbase.com
```

确保在云函数中的 `SNAPSHOT_SERVICE_URL` 配置正确。

## 测试验证

### 1. 测试云函数
```javascript
// 在云开发控制台测试
{
  "resumeData": {
    "personalInfo": {
      "name": "张三",
      "email": "<EMAIL>", 
      "phone": "13800138000"
    },
    "summary": "资深前端开发工程师",
    "workExperience": [
      {
        "position": "高级前端工程师",
        "company": "腾讯科技有限公司",
        "startDate": "2020-01",
        "endDate": "至今",
        "description": "负责微信小程序前端开发"
      }
    ],
    "education": [
      {
        "degree": "本科",
        "major": "计算机科学与技术", 
        "school": "清华大学",
        "startDate": "2016-09",
        "endDate": "2020-06"
      }
    ],
    "skills": ["JavaScript", "React", "Vue", "Node.js"]
  },
  "format": "png"
}
```

### 2. 测试微信云托管服务
```bash
curl -X POST https://resume-snapshot-zemuresume-4gjvx1wea78e3d1e.ap-shanghai.run.tcloudbase.com/snapshot \
  -H "Content-Type: application/json" \
  -d '{
    "url": "https://example.com",
    "format": "png",
    "width": 1240,
    "height": 1754
  }' \
  --output test-screenshot.png
```

## 性能指标

- ✅ 简历预览生成时间：≤ 10秒
- ✅ 功能成功率：≥ 95%
- ✅ 使用真实数据测试，禁止模拟数据

## 控制台管理

### 云函数管理
- [云函数控制台](https://tcb.cloud.tencent.com/dev?envId=zemuresume-4gjvx1wea78e3d1e#/scf/detail?id=resumePreviewGenerator&NameSpace=zemuresume-4gjvx1wea78e3d1e)

### 微信云托管管理  
- [微信云托管控制台](https://cloud.weixin.qq.com/cloudrun)

### 云存储管理
- [云存储控制台](https://console.cloud.tencent.com/tcb/storage)

## 故障排查

### 1. 微信云托管服务无法访问
- 检查服务是否正常运行
- 确认网络连接
- 查看服务日志

### 2. 截图生成失败
- 检查HTML URL是否可访问
- 确认Puppeteer依赖是否正确安装
- 查看容器日志

### 3. 云函数调用失败
- 检查axios依赖是否安装
- 确认服务地址配置正确
- 查看云函数日志

## 后续优化

1. **缓存机制**：为相同简历内容实现缓存
2. **多模板支持**：支持不同的简历模板样式
3. **批量处理**：支持批量生成简历预览
4. **性能监控**：添加详细的性能监控和告警

## 联系支持

如遇到部署问题，请查看：
- [微信云托管文档](https://developers.weixin.qq.com/miniprogram/dev/wxcloudservice/wxcloudrun/src/)
- [云开发文档](https://developers.weixin.qq.com/miniprogram/dev/wxcloud/)
