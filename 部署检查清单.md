# 🚀 微信云托管部署检查清单

## 📋 部署前准备

### ✅ 环境信息确认
- [ ] 微信AppID: `wx4fa04593aaf3bb76`
- [ ] 环境ID: `prod-2gczdho212bd52ca`
- [ ] 服务名称: `ai-resume-snapshot`
- [ ] 项目路径: `/Users/<USER>/Desktop/ai-resume/cloud-run/resume-snapshot`

### ✅ 文件准备
- [ ] Dockerfile 存在且已修复npm安装问题
- [ ] wxcloud.config.js 配置文件已创建
- [ ] package.json 包含正确的依赖
- [ ] index.js 主程序文件完整

### ✅ 代码包准备
- [ ] 运行打包脚本: `./prepare-deployment.sh`
- [ ] 生成压缩包: `ai-resume-snapshot-YYYYMMDD-HHMMSS.tar.gz`
- [ ] 确认文件大小合理（通常 < 50MB）

## 🌐 控制台部署

### ✅ 访问控制台
- [ ] 打开: https://cloud.weixin.qq.com/cloudrun
- [ ] 微信扫码登录成功
- [ ] 选择环境: `prod`

### ✅ 创建服务
- [ ] 点击"新建服务"
- [ ] 服务名称: `ai-resume-snapshot`
- [ ] 服务描述: `AI简历截图服务 - 将HTML简历转换为PNG预览图`
- [ ] 部署方式: `上传代码包`

### ✅ 配置参数
- [ ] 容器端口: `80`
- [ ] CPU: `1核`
- [ ] 内存: `2GB`
- [ ] 最小实例数: `0`
- [ ] 最大实例数: `10`
- [ ] 公网访问: `开启`

### ✅ 上传代码包
- [ ] 选择准备好的压缩包文件
- [ ] 等待上传完成（显示100%）
- [ ] 构建方式: `Dockerfile`
- [ ] Dockerfile路径: `./Dockerfile`

### ✅ 版本发布
- [ ] 版本名称: `v1.0.0`
- [ ] 版本描述: `AI简历截图服务初始版本`
- [ ] 发布策略: `全量发布`
- [ ] 流量分配: `100%`

### ✅ 确认部署
- [ ] 检查所有配置信息
- [ ] 点击"确认部署"
- [ ] 等待部署完成（5-15分钟）

## 🔍 部署后验证

### ✅ 服务状态检查
- [ ] 服务状态: `运行中`
- [ ] 实例数量: ≥ 1
- [ ] 访问地址已生成

### ✅ 获取服务地址
- [ ] 复制访问地址（格式类似）:
  ```
  https://ai-resume-snapshot-prod-2gczdho212bd52ca.ap-shanghai.run.tcloudbase.com
  ```

### ✅ 基础功能测试
- [ ] 使用curl测试服务响应:
  ```bash
  curl https://ai-resume-snapshot-prod-2gczdho212bd52ca.ap-shanghai.run.tcloudbase.com/health
  ```
- [ ] 返回状态码: 200 或 404（正常，说明服务在运行）

## 🔧 云函数更新

### ✅ 更新服务地址
- [ ] 打开文件: `cloudfunctions/resumePreviewGenerator/index.js`
- [ ] 找到: `const SNAPSHOT_SERVICE_URL = '...'`
- [ ] 替换为实际的微信云托管服务地址
- [ ] 保存文件

### ✅ 重新部署云函数
- [ ] 使用CloudBase CLI部署:
  ```bash
  cloudbase functions:deploy resumePreviewGenerator
  ```
- [ ] 或通过CloudBase控制台手动上传

## 🧪 完整功能测试

### ✅ 云函数测试
- [ ] 在CloudBase控制台测试云函数
- [ ] 使用测试数据:
  ```json
  {
    "resumeData": {
      "personalInfo": {
        "name": "张三",
        "email": "<EMAIL>",
        "phone": "13800138000"
      },
      "summary": "资深开发工程师",
      "workExperience": [
        {
          "position": "高级工程师",
          "company": "科技公司",
          "startDate": "2020-01",
          "endDate": "至今",
          "description": "负责系统开发"
        }
      ],
      "skills": ["JavaScript", "Node.js", "React"]
    },
    "format": "png"
  }
  ```

### ✅ 预期结果验证
- [ ] HTML生成成功
- [ ] HTML URL可访问
- [ ] PNG截图生成成功（不再是占位图片）
- [ ] PNG URL可访问且显示真实截图
- [ ] 总响应时间 < 10秒
- [ ] 无错误信息

## 📊 性能监控

### ✅ 设置监控
- [ ] 在微信云托管控制台设置监控告警
- [ ] 监控指标:
  - CPU使用率 < 80%
  - 内存使用率 < 80%
  - 请求成功率 > 95%
  - 平均响应时间 < 10秒

### ✅ 日志检查
- [ ] 查看微信云托管服务日志
- [ ] 查看CloudBase云函数日志
- [ ] 确认无错误日志

## 🎯 最终验证

### ✅ 端到端测试
- [ ] 从小程序或Web应用调用简历预览功能
- [ ] 生成的PNG图片质量良好
- [ ] 用户体验流畅
- [ ] 系统稳定运行

### ✅ 文档更新
- [ ] 更新README.md文档
- [ ] 记录实际的服务地址
- [ ] 更新API文档

## 🎉 部署完成确认

- [ ] **所有检查项目已完成**
- [ ] **系统功能正常**
- [ ] **性能指标达标**
- [ ] **监控告警已设置**

---

**恭喜！AI简历系统微信云托管部署成功！** 🎊

## 📞 问题排查

如果遇到问题，请检查：

1. **部署失败**: 查看构建日志，检查Dockerfile和依赖
2. **服务启动失败**: 检查端口配置和应用启动命令
3. **访问超时**: 检查健康检查配置和应用响应时间
4. **截图失败**: 检查Puppeteer依赖和内存配置

**技术支持**: 微信云托管控制台右下角帮助按钮
