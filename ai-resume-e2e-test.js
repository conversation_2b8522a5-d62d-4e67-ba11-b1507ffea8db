/**
 * AI简历系统端到端功能测试
 * 测试范围：简历生成、预览、截图功能
 * 测试环境：微信云托管 + 云函数
 */

const fs = require('fs');
const path = require('path');

// 测试配置
const TEST_CONFIG = {
    // 云开发环境配置
    envId: 'zemuresume-4gjvx1wea78e3d1e',

    // 静态托管域名
    staticDomain: 'zemuresume-4gjvx1wea78e3d1e-1341667342.tcloudbaseapp.com',

    // 云托管服务地址 (需要确认实际地址)
    cloudRunUrl: 'https://resume-snapshot-zemuresume-4gjvx1wea78e3d1e.service.tcloudbase.com',

    // 测试超时时间
    timeout: 60000, // 60秒

    // 性能要求
    performance: {
        maxResponseTime: 60000, // 最大响应时间60秒
        minSuccessRate: 0.95    // 最小成功率95%
    }
};

// 测试数据
const TEST_DATA = {
    // 测试简历内容
    resumeContent: `
    姓名：张三
    电话：13800138000
    邮箱：<EMAIL>
    
    工作经历：
    2020.01-至今 高级前端工程师 - 某科技公司
    - 负责公司核心产品的前端开发工作
    - 使用React、Vue等技术栈开发用户界面
    - 优化页面性能，提升用户体验
    
    技能：
    - 前端开发：JavaScript、TypeScript、React、Vue
    - 后端开发：Node.js、Python
    - 数据库：MySQL、MongoDB
    `,

    // 测试职位描述
    jobDescription: `
    职位：高级前端工程师
    要求：
    - 3年以上前端开发经验
    - 熟练掌握React、Vue等前端框架
    - 具备良好的代码规范和团队协作能力
    - 有移动端开发经验优先
    `
};

/**
 * 发送HTTP请求
 */
async function makeRequest(url, options = {}) {
    const fetch = (await import('node-fetch')).default;

    const startTime = Date.now();

    try {
        console.log(`📡 发送请求: ${options.method || 'GET'} ${url}`);

        const response = await fetch(url, {
            timeout: TEST_CONFIG.timeout,
            ...options
        });

        const endTime = Date.now();
        const responseTime = endTime - startTime;

        const result = {
            ok: response.ok,
            status: response.status,
            statusText: response.statusText,
            responseTime: responseTime,
            headers: Object.fromEntries(response.headers.entries())
        };

        // 根据Content-Type处理响应数据
        const contentType = response.headers.get('content-type') || '';

        if (contentType.includes('application/json')) {
            result.data = await response.json();
        } else if (contentType.includes('image/') || contentType.includes('application/pdf')) {
            result.data = await response.buffer();
        } else {
            result.data = await response.text();
        }

        console.log(`⏱️ 响应时间: ${responseTime}ms, 状态: ${response.status}`);

        return result;

    } catch (error) {
        const endTime = Date.now();
        const responseTime = endTime - startTime;

        console.error(`❌ 请求失败: ${error.message}`);

        return {
            ok: false,
            error: error.message,
            responseTime: responseTime
        };
    }
}

/**
 * 测试云函数调用
 */
async function testCloudFunction(functionName, data = {}) {
    console.log(`🔧 测试云函数: ${functionName}`);

    const startTime = Date.now();

    try {
        // 使用云开发SDK调用云函数
        const cloudbase = require('@cloudbase/node-sdk');

        const app = cloudbase.init({
            env: TEST_CONFIG.envId
        });

        const result = await app.callFunction({
            name: functionName,
            data: data
        });

        const endTime = Date.now();
        const responseTime = endTime - startTime;

        console.log(`✅ 云函数 ${functionName} 调用成功 (${responseTime}ms)`);
        console.log(`📄 返回数据大小: ${JSON.stringify(result.result).length} 字符`);

        return {
            success: true,
            result: result.result,
            requestId: result.requestId,
            responseTime: responseTime
        };

    } catch (error) {
        const endTime = Date.now();
        const responseTime = endTime - startTime;

        console.error(`❌ 云函数 ${functionName} 调用失败 (${responseTime}ms):`, error.message);
        return {
            success: false,
            error: error.message,
            responseTime: responseTime
        };
    }
}

/**
 * 测试简历生成功能
 */
async function testResumeGeneration() {
    console.log('\n📝 测试简历生成功能...');

    const testResults = [];

    // 1. 测试简历解析
    console.log('1️⃣ 测试简历解析...');
    const parseResult = await testCloudFunction('resumeWorker', {
        action: 'parse',
        content: TEST_DATA.resumeContent
    });
    testResults.push({
        test: '简历解析',
        success: parseResult.success,
        responseTime: parseResult.responseTime || 0
    });

    // 2. 测试职位分析
    console.log('2️⃣ 测试职位分析...');
    const jdResult = await testCloudFunction('jdWorker', {
        jobDescription: TEST_DATA.jobDescription
    });
    testResults.push({
        test: '职位分析',
        success: jdResult.success,
        responseTime: jdResult.responseTime || 0
    });

    // 3. 测试简历生成
    console.log('3️⃣ 测试简历生成...');
    const generateResult = await testCloudFunction('cvGenerator', {
        resumeData: parseResult.result,
        jobRequirements: jdResult.result
    });
    testResults.push({
        test: '简历生成',
        success: generateResult.success,
        responseTime: generateResult.responseTime || 0
    });

    return {
        testResults,
        generatedResume: generateResult.success ? generateResult.result : null
    };
}

/**
 * 测试简历预览功能
 */
async function testResumePreview(resumeData) {
    console.log('\n🖼️ 测试简历预览功能...');

    if (!resumeData) {
        console.log('❌ 没有简历数据，跳过预览测试');
        return { success: false, error: '缺少简历数据' };
    }

    // 测试预览页面生成
    const previewResult = await testCloudFunction('resumePreviewGenerator', {
        resumeData: resumeData
    });

    if (!previewResult.success) {
        return { success: false, error: previewResult.error };
    }

    return {
        success: true,
        previewUrl: previewResult.result.previewUrl
    };
}

/**
 * 测试截图生成功能
 */
async function testSnapshotGeneration(previewUrl) {
    console.log('\n📸 测试截图生成功能...');

    if (!previewUrl) {
        console.log('❌ 没有预览URL，跳过截图测试');
        return { success: false, error: '缺少预览URL' };
    }

    // 调用云托管截图服务
    const snapshotData = {
        url: previewUrl,
        format: 'png',
        width: 1240,
        height: 1754,
        options: {
            fullPage: true,
            quality: 90,
            delay: 2000
        }
    };

    const result = await makeRequest(`${TEST_CONFIG.cloudRunUrl}/snapshot`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(snapshotData)
    });

    if (result.ok && result.data) {
        // 保存截图文件
        const outputDir = path.join(__dirname, 'test-output');
        if (!fs.existsSync(outputDir)) {
            fs.mkdirSync(outputDir, { recursive: true });
        }

        const filename = `resume-snapshot-${Date.now()}.png`;
        const filepath = path.join(outputDir, filename);

        fs.writeFileSync(filepath, result.data);

        console.log(`✅ 截图生成成功:`);
        console.log(`   文件大小: ${result.data.length} bytes`);
        console.log(`   保存路径: ${filepath}`);

        return {
            success: true,
            filepath: filepath,
            fileSize: result.data.length,
            responseTime: result.responseTime
        };
    } else {
        console.error(`❌ 截图生成失败:`, result.error || result.statusText);
        return {
            success: false,
            error: result.error || result.statusText
        };
    }
}

/**
 * 生成测试报告
 */
function generateTestReport(results) {
    const report = {
        timestamp: new Date().toISOString(),
        environment: {
            envId: TEST_CONFIG.envId,
            staticDomain: TEST_CONFIG.staticDomain,
            cloudRunUrl: TEST_CONFIG.cloudRunUrl
        },
        summary: {
            totalTests: 0,
            passedTests: 0,
            failedTests: 0,
            successRate: 0,
            averageResponseTime: 0
        },
        details: results,
        performance: {
            meetsResponseTimeRequirement: true,
            meetsSuccessRateRequirement: true
        }
    };

    // 计算统计信息
    let totalResponseTime = 0;
    let responseTimeCount = 0;

    results.forEach(result => {
        report.summary.totalTests++;
        if (result.success) {
            report.summary.passedTests++;
        } else {
            report.summary.failedTests++;
        }

        if (result.responseTime) {
            totalResponseTime += result.responseTime;
            responseTimeCount++;
        }
    });

    report.summary.successRate = report.summary.passedTests / report.summary.totalTests;
    report.summary.averageResponseTime = responseTimeCount > 0 ? totalResponseTime / responseTimeCount : 0;

    // 检查性能要求
    report.performance.meetsResponseTimeRequirement = report.summary.averageResponseTime <= TEST_CONFIG.performance.maxResponseTime;
    report.performance.meetsSuccessRateRequirement = report.summary.successRate >= TEST_CONFIG.performance.minSuccessRate;

    return report;
}

/**
 * 主测试函数
 */
async function runE2ETest() {
    console.log('🚀 开始AI简历系统端到端功能测试\n');
    console.log(`📋 测试环境: ${TEST_CONFIG.envId}`);
    console.log(`🌐 静态托管: ${TEST_CONFIG.staticDomain}`);
    console.log(`☁️ 云托管服务: ${TEST_CONFIG.cloudRunUrl}\n`);

    const allResults = [];

    try {
        // 1. 测试简历生成功能
        const generationTest = await testResumeGeneration();
        allResults.push(...generationTest.testResults);

        // 2. 测试简历预览功能
        const previewTest = await testResumePreview(generationTest.generatedResume);
        allResults.push({
            test: '简历预览',
            success: previewTest.success,
            error: previewTest.error,
            responseTime: previewTest.responseTime || 0
        });

        // 3. 测试截图生成功能
        const snapshotTest = await testSnapshotGeneration(previewTest.previewUrl);
        allResults.push({
            test: '截图生成',
            success: snapshotTest.success,
            error: snapshotTest.error,
            responseTime: snapshotTest.responseTime || 0,
            fileSize: snapshotTest.fileSize,
            filepath: snapshotTest.filepath
        });

        // 生成测试报告
        const report = generateTestReport(allResults);

        // 保存测试报告
        const reportPath = path.join(__dirname, 'test-output', `e2e-test-report-${Date.now()}.json`);
        fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));

        console.log('\n📊 测试完成！');
        console.log(`📄 测试报告: ${reportPath}`);
        console.log(`✅ 成功率: ${(report.summary.successRate * 100).toFixed(1)}%`);
        console.log(`⏱️ 平均响应时间: ${report.summary.averageResponseTime.toFixed(0)}ms`);

        if (report.performance.meetsSuccessRateRequirement && report.performance.meetsResponseTimeRequirement) {
            console.log('🎉 所有性能要求均已满足！');
        } else {
            console.log('⚠️ 部分性能要求未满足');
        }

        return report;

    } catch (error) {
        console.error('💥 测试过程中发生错误:', error);
        return null;
    }
}

// 运行测试
if (require.main === module) {
    runE2ETest().catch(console.error);
}

module.exports = {
    runE2ETest,
    testResumeGeneration,
    testResumePreview,
    testSnapshotGeneration
};
