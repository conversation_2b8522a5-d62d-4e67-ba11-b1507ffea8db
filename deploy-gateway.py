#!/usr/bin/env python3
"""
部署gateway函数的Python脚本
"""

import base64
import json
import os
from tencentcloud.common import credential
from tencentcloud.common.profile.client_profile import ClientProfile
from tencentcloud.common.profile.http_profile import HttpProfile
from tencentcloud.scf.v20180416 import scf_client, models

# 腾讯云认证配置
SECRET_ID = "YOUR_SECRET_ID"
SECRET_KEY = "YOUR_SECRET_KEY"

def deploy_gateway():
    """部署gateway函数"""
    try:
        # 初始化客户端
        cred = credential.Credential(SECRET_ID, SECRET_KEY)
        httpProfile = HttpProfile()
        httpProfile.endpoint = "scf.ap-guangzhou.tencentcloudapi.com"
        
        clientProfile = ClientProfile()
        clientProfile.httpProfile = httpProfile
        
        client = scf_client.ScfClient(cred, "ap-guangzhou", clientProfile)
        
        # 读取zip文件并编码
        zip_file_path = "gateway-fixed.zip"
        if not os.path.exists(zip_file_path):
            print(f"❌ 文件不存在: {zip_file_path}")
            return False
            
        with open(zip_file_path, 'rb') as f:
            zip_content = f.read()
            
        zip_base64 = base64.b64encode(zip_content).decode('utf-8')
        
        print(f"📦 准备部署gateway函数，文件大小: {len(zip_content)} bytes")
        
        # 更新函数代码
        req = models.UpdateFunctionCodeRequest()
        params = {
            "FunctionName": "gateway",
            "ZipFile": zip_base64
        }
        req.from_json_string(json.dumps(params))
        
        resp = client.UpdateFunctionCode(req)
        print("✅ gateway函数部署成功")
        print(f"   RequestId: {resp.RequestId}")
        
        return True
        
    except Exception as e:
        print(f"❌ gateway函数部署失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 开始部署gateway函数...")
    success = deploy_gateway()
    
    if success:
        print("\n🎉 部署完成！")
        print("⏳ 等待30秒让函数生效...")
        import time
        time.sleep(30)
        
        print("\n🧪 测试函数连通性...")
        import requests
        try:
            response = requests.get("http://1341667342-digl3p9k4d.ap-guangzhou.tencentscf.com", timeout=10)
            print(f"   HTTP状态码: {response.status_code}")
            if response.status_code in [200, 404, 405]:
                print("   ✅ 连通性正常")
            else:
                print("   ❌ 连通性异常")
        except Exception as e:
            print(f"   ❌ 连通性测试失败: {e}")
    else:
        print("\n💥 部署失败")
